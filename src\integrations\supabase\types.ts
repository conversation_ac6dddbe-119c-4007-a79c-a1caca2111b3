export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          full_name: string | null
          avatar_url: string | null
          timezone: string
          theme_preference: string
          biometric_enabled: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          avatar_url?: string | null
          timezone?: string
          theme_preference?: string
          biometric_enabled?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          avatar_url?: string | null
          timezone?: string
          theme_preference?: string
          biometric_enabled?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      tasks: {
        Row: {
          id: string
          user_id: string
          title: string
          description: string | null
          priority: 'low' | 'medium' | 'high'
          status: 'pending' | 'in_progress' | 'completed' | 'cancelled'
          folder: string
          due_date: string | null
          completed_at: string | null
          tags: string[]
          starred: boolean
          archived: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          title: string
          description?: string | null
          priority?: 'low' | 'medium' | 'high'
          status?: 'pending' | 'in_progress' | 'completed' | 'cancelled'
          folder?: string
          due_date?: string | null
          completed_at?: string | null
          tags?: string[]
          starred?: boolean
          archived?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          title?: string
          description?: string | null
          priority?: 'low' | 'medium' | 'high'
          status?: 'pending' | 'in_progress' | 'completed' | 'cancelled'
          folder?: string
          due_date?: string | null
          completed_at?: string | null
          tags?: string[]
          starred?: boolean
          archived?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      subtasks: {
        Row: {
          id: string
          task_id: string
          title: string
          completed: boolean
          created_at: string
        }
        Insert: {
          id?: string
          task_id: string
          title: string
          completed?: boolean
          created_at?: string
        }
        Update: {
          id?: string
          task_id?: string
          title?: string
          completed?: boolean
          created_at?: string
        }
      }
      finance_items: {
        Row: {
          id: string
          user_id: string
          description: string
          amount: number
          type: 'owed' | 'owing'
          due_date: string | null
          person_name: string
          person_email: string | null
          person_phone: string | null
          paid: boolean
          category: string
          priority: 'low' | 'medium' | 'high'
          payment_method: string | null
          payment_link: string | null
          notes: string | null
          reminder_set: boolean
          reminder_date: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          description: string
          amount: number
          type: 'owed' | 'owing'
          due_date?: string | null
          person_name: string
          person_email?: string | null
          person_phone?: string | null
          paid?: boolean
          category?: string
          priority?: 'low' | 'medium' | 'high'
          payment_method?: string | null
          payment_link?: string | null
          notes?: string | null
          reminder_set?: boolean
          reminder_date?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          description?: string
          amount?: number
          type?: 'owed' | 'owing'
          due_date?: string | null
          person_name?: string
          person_email?: string | null
          person_phone?: string | null
          paid?: boolean
          category?: string
          priority?: 'low' | 'medium' | 'high'
          payment_method?: string | null
          payment_link?: string | null
          notes?: string | null
          reminder_set?: boolean
          reminder_date?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      notes: {
        Row: {
          id: string
          user_id: string
          title: string
          content: string
          tags: string[]
          encrypted: boolean
          pin_protected: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          title: string
          content: string
          tags?: string[]
          encrypted?: boolean
          pin_protected?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          title?: string
          content?: string
          tags?: string[]
          encrypted?: boolean
          pin_protected?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      calendar_events: {
        Row: {
          id: string
          user_id: string
          title: string
          description: string | null
          start_time: string
          end_time: string
          all_day: boolean
          location: string | null
          color: string
          reminder_minutes: number | null
          recurring: boolean
          recurrence_rule: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          title: string
          description?: string | null
          start_time: string
          end_time: string
          all_day?: boolean
          location?: string | null
          color?: string
          reminder_minutes?: number | null
          recurring?: boolean
          recurrence_rule?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          title?: string
          description?: string | null
          start_time?: string
          end_time?: string
          all_day?: boolean
          location?: string | null
          color?: string
          reminder_minutes?: number | null
          recurring?: boolean
          recurrence_rule?: string | null
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      task_priority: 'low' | 'medium' | 'high'
      task_status: 'pending' | 'in_progress' | 'completed' | 'cancelled'
      finance_type: 'owed' | 'owing'
      habit_frequency: 'daily' | 'weekly' | 'monthly'
      mood_type: 'happy' | 'sad' | 'anxious' | 'calm' | 'angry' | 'excited' | 'tired' | 'grateful'
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row']
export type TablesInsert<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Insert']
export type TablesUpdate<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Update']
export type Enums<T extends keyof Database['public']['Enums']> = Database['public']['Enums'][T]


