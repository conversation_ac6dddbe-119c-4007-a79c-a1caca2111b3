import { Plus, Calendar, CheckSquare, DollarSign, MessageSquare, ShoppingCart, Star, StickyNote, Heart } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";

export default function QuickActions() {
  const navigate = useNavigate();

  const actions = [
    { icon: CheckSquare, label: "Add Task", color: "text-green-500", path: "/tasks" },
    { icon: Calendar, label: "Add Event", color: "text-primary", path: "/calendar" },
    { icon: DollarSign, label: "Add Finance", color: "text-yellow-500", path: "/finance" },
    { icon: MessageSquare, label: "Chat AI", color: "text-purple-500", path: "/chat" },
    { icon: ShoppingCart, label: "Shopping", color: "text-blue-500", path: "/shopping" },
    { icon: Star, label: "Wishlist", color: "text-pink-500", path: "/wishlist" },
    { icon: StickyNote, label: "Notes", color: "text-orange-500", path: "/notes" },
    { icon: Heart, label: "Affirmations", color: "text-red-500", path: "/affirmations" },
  ];

  return (
    <div className="space-y-4">
      <h3 className="font-semibold text-foreground">Quick Actions</h3>
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-3">
        {actions.map((action, index) => (
          <Button
            key={index}
            variant="outline"
            onClick={() => navigate(action.path)}
            className="vanta-button h-auto p-4 flex flex-col items-center gap-2 border-border hover:border-primary/30 hover:bg-white/5 transition-all duration-300 group"
          >
            <action.icon className={`h-5 w-5 ${action.color} group-hover:scale-110 transition-transform`} />
            <span className="text-sm font-medium">{action.label}</span>
          </Button>
        ))}
      </div>
    </div>
  );
}