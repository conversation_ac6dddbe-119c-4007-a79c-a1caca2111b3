import { useState, useEffect } from "react";
import { DollarSign, TrendingUp, TrendingDown, AlertCircle, CheckCircle } from "lucide-react";
import { formatCurrency } from "@/utils/currency";

interface FinancialStats {
  totalOwed: number;
  totalOwing: number;
  overdueItems: number;
  completedToday: number;
}

export default function QuickStats() {
  const [stats, setStats] = useState<FinancialStats>({
    totalOwed: 0,
    totalOwing: 0,
    overdueItems: 0,
    completedToday: 0
  });

  useEffect(() => {
    // In a real app, this would fetch from Supabase
    // For now, we'll use mock data
    setStats({
      totalOwed: 250.00,
      totalOwing: 75.50,
      overdueItems: 1,
      completedToday: 5
    });
  }, []);

  const netBalance = stats.totalOwed - stats.totalOwing;
  const isPositive = netBalance >= 0;

  return (
    <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
      {/* Money Owed to You */}
      <div className="vanta-card p-4 bg-gradient-to-br from-green-500/10 to-emerald-500/5 border-green-500/20">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-green-500/20 rounded-lg">
            <TrendingUp className="h-4 w-4 text-green-500" />
          </div>
          <div>
            <p className="text-xs text-muted-foreground">Owed to You</p>
            <p className="text-lg font-bold text-green-500">{formatCurrency(stats.totalOwed)}</p>
          </div>
        </div>
      </div>

      {/* Money You Owe */}
      <div className="vanta-card p-4 bg-gradient-to-br from-red-500/10 to-rose-500/5 border-red-500/20">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-red-500/20 rounded-lg">
            <TrendingDown className="h-4 w-4 text-red-500" />
          </div>
          <div>
            <p className="text-xs text-muted-foreground">You Owe</p>
            <p className="text-lg font-bold text-red-500">{formatCurrency(stats.totalOwing)}</p>
          </div>
        </div>
      </div>

      {/* Overdue Items */}
      <div className="vanta-card p-4 bg-gradient-to-br from-yellow-500/10 to-orange-500/5 border-yellow-500/20">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-yellow-500/20 rounded-lg">
            <AlertCircle className="h-4 w-4 text-yellow-500" />
          </div>
          <div>
            <p className="text-xs text-muted-foreground">Overdue</p>
            <p className="text-lg font-bold text-yellow-500">{stats.overdueItems}</p>
          </div>
        </div>
      </div>

      {/* Completed Today */}
      <div className="vanta-card p-4 bg-gradient-to-br from-blue-500/10 to-cyan-500/5 border-blue-500/20">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-blue-500/20 rounded-lg">
            <CheckCircle className="h-4 w-4 text-blue-500" />
          </div>
          <div>
            <p className="text-xs text-muted-foreground">Completed</p>
            <p className="text-lg font-bold text-blue-500">{stats.completedToday}</p>
          </div>
        </div>
      </div>

      {/* Net Balance Summary */}
      <div className="col-span-2 lg:col-span-4">
        <div className={`vanta-card p-4 bg-gradient-to-br ${
          isPositive 
            ? 'from-green-500/10 to-emerald-500/5 border-green-500/20' 
            : 'from-red-500/10 to-rose-500/5 border-red-500/20'
        }`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className={`p-2 rounded-lg ${
                isPositive ? 'bg-green-500/20' : 'bg-red-500/20'
              }`}>
                <DollarSign className={`h-5 w-5 ${
                  isPositive ? 'text-green-500' : 'text-red-500'
                }`} />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Net Balance</p>
                <p className={`text-xl font-bold ${
                  isPositive ? 'text-green-500' : 'text-red-500'
                }`}>
                  {isPositive ? '+' : ''}{formatCurrency(Math.abs(netBalance))}
                </p>
              </div>
            </div>
            <div className="text-right">
              <p className="text-xs text-muted-foreground">
                {isPositive ? 'You\'re ahead!' : 'Pay attention to debts'}
              </p>
              <p className="text-xs text-muted-foreground">
                {stats.overdueItems > 0 && `${stats.overdueItems} overdue item${stats.overdueItems > 1 ? 's' : ''}`}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
