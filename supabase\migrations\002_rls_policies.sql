-- Row Level Security Policies for Vanta App

-- Profiles policies
CREATE POLICY "Users can view own profile" ON profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Tasks policies
CREATE POLICY "Users can view own tasks" ON tasks
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own tasks" ON tasks
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own tasks" ON tasks
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own tasks" ON tasks
    FOR DELETE USING (auth.uid() = user_id);

-- Subtasks policies
CREATE POLICY "Users can view own subtasks" ON subtasks
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM tasks 
            WHERE tasks.id = subtasks.task_id 
            AND tasks.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert own subtasks" ON subtasks
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM tasks 
            WHERE tasks.id = subtasks.task_id 
            AND tasks.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update own subtasks" ON subtasks
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM tasks 
            WHERE tasks.id = subtasks.task_id 
            AND tasks.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete own subtasks" ON subtasks
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM tasks 
            WHERE tasks.id = subtasks.task_id 
            AND tasks.user_id = auth.uid()
        )
    );

-- Finance items policies
CREATE POLICY "Users can view own finance items" ON finance_items
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own finance items" ON finance_items
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own finance items" ON finance_items
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own finance items" ON finance_items
    FOR DELETE USING (auth.uid() = user_id);

-- Shopping lists policies
CREATE POLICY "Users can view own shopping lists" ON shopping_lists
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own shopping lists" ON shopping_lists
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own shopping lists" ON shopping_lists
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own shopping lists" ON shopping_lists
    FOR DELETE USING (auth.uid() = user_id);

-- Shopping items policies
CREATE POLICY "Users can view own shopping items" ON shopping_items
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM shopping_lists 
            WHERE shopping_lists.id = shopping_items.list_id 
            AND shopping_lists.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert own shopping items" ON shopping_items
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM shopping_lists 
            WHERE shopping_lists.id = shopping_items.list_id 
            AND shopping_lists.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update own shopping items" ON shopping_items
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM shopping_lists 
            WHERE shopping_lists.id = shopping_items.list_id 
            AND shopping_lists.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete own shopping items" ON shopping_items
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM shopping_lists 
            WHERE shopping_lists.id = shopping_items.list_id 
            AND shopping_lists.user_id = auth.uid()
        )
    );

-- Wishlist items policies
CREATE POLICY "Users can view own wishlist items" ON wishlist_items
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own wishlist items" ON wishlist_items
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own wishlist items" ON wishlist_items
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own wishlist items" ON wishlist_items
    FOR DELETE USING (auth.uid() = user_id);

-- Habits policies
CREATE POLICY "Users can view own habits" ON habits
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own habits" ON habits
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own habits" ON habits
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own habits" ON habits
    FOR DELETE USING (auth.uid() = user_id);

-- Habit entries policies
CREATE POLICY "Users can view own habit entries" ON habit_entries
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM habits 
            WHERE habits.id = habit_entries.habit_id 
            AND habits.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert own habit entries" ON habit_entries
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM habits 
            WHERE habits.id = habit_entries.habit_id 
            AND habits.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update own habit entries" ON habit_entries
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM habits 
            WHERE habits.id = habit_entries.habit_id 
            AND habits.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete own habit entries" ON habit_entries
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM habits 
            WHERE habits.id = habit_entries.habit_id 
            AND habits.user_id = auth.uid()
        )
    );

-- Calendar events policies
CREATE POLICY "Users can view own calendar events" ON calendar_events
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own calendar events" ON calendar_events
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own calendar events" ON calendar_events
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own calendar events" ON calendar_events
    FOR DELETE USING (auth.uid() = user_id);

-- Chat messages policies
CREATE POLICY "Users can view own chat messages" ON chat_messages
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own chat messages" ON chat_messages
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own chat messages" ON chat_messages
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own chat messages" ON chat_messages
    FOR DELETE USING (auth.uid() = user_id);

-- Mood entries policies
CREATE POLICY "Users can view own mood entries" ON mood_entries
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own mood entries" ON mood_entries
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own mood entries" ON mood_entries
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own mood entries" ON mood_entries
    FOR DELETE USING (auth.uid() = user_id);

-- Notes policies
CREATE POLICY "Users can view own notes" ON notes
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own notes" ON notes
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own notes" ON notes
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own notes" ON notes
    FOR DELETE USING (auth.uid() = user_id);

-- Pomodoro sessions policies
CREATE POLICY "Users can view own pomodoro sessions" ON pomodoro_sessions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own pomodoro sessions" ON pomodoro_sessions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own pomodoro sessions" ON pomodoro_sessions
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own pomodoro sessions" ON pomodoro_sessions
    FOR DELETE USING (auth.uid() = user_id);
