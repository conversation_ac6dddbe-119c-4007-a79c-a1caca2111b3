import { useState, useEffect, useRef } from "react";
import { Play, Pause, RotateCcw, Settings, Coffee, Target, TrendingUp } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface PomodoroSession {
  id: string;
  type: "work" | "shortBreak" | "longBreak";
  duration: number;
  completedAt: Date;
}

export default function PomodoroTimer() {
  const [timeLeft, setTimeLeft] = useState(25 * 60); // 25 minutes in seconds
  const [isActive, setIsActive] = useState(false);
  const [currentSession, setCurrentSession] = useState<"work" | "shortBreak" | "longBreak">("work");
  const [completedSessions, setCompletedSessions] = useState<PomodoroSession[]>([]);
  const [workDuration, setWorkDuration] = useState(25);
  const [shortBreakDuration, setShortBreakDuration] = useState(5);
  const [longBreakDuration, setLongBreakDuration] = useState(15);
  const [showSettings, setShowSettings] = useState(false);
  
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  const sessionDurations = {
    work: workDuration * 60,
    shortBreak: shortBreakDuration * 60,
    longBreak: longBreakDuration * 60,
  };

  useEffect(() => {
    if (isActive && timeLeft > 0) {
      intervalRef.current = setInterval(() => {
        setTimeLeft((time) => time - 1);
      }, 1000);
    } else if (timeLeft === 0) {
      handleSessionComplete();
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isActive, timeLeft]);

  const handleSessionComplete = () => {
    setIsActive(false);
    
    // Play notification sound (you would add actual audio file)
    // audioRef.current?.play();
    
    // Add completed session
    const session: PomodoroSession = {
      id: Date.now().toString(),
      type: currentSession,
      duration: sessionDurations[currentSession],
      completedAt: new Date(),
    };
    setCompletedSessions(prev => [...prev, session]);

    // Auto-switch to next session
    if (currentSession === "work") {
      const workSessions = completedSessions.filter(s => s.type === "work").length + 1;
      const nextSession = workSessions % 4 === 0 ? "longBreak" : "shortBreak";
      setCurrentSession(nextSession);
      setTimeLeft(sessionDurations[nextSession]);
    } else {
      setCurrentSession("work");
      setTimeLeft(sessionDurations.work);
    }

    // Show notification
    if (Notification.permission === "granted") {
      new Notification(`${currentSession === "work" ? "Work" : "Break"} session completed!`, {
        body: `Time for a ${currentSession === "work" ? "break" : "work session"}`,
        icon: "/assets/Vanta App Icon.png"
      });
    }
  };

  const toggleTimer = () => {
    setIsActive(!isActive);
  };

  const resetTimer = () => {
    setIsActive(false);
    setTimeLeft(sessionDurations[currentSession]);
  };

  const switchSession = (type: "work" | "shortBreak" | "longBreak") => {
    setCurrentSession(type);
    setTimeLeft(sessionDurations[type]);
    setIsActive(false);
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
  };

  const getProgress = () => {
    const totalTime = sessionDurations[currentSession];
    return ((totalTime - timeLeft) / totalTime) * 100;
  };

  const getSessionIcon = (type: string) => {
    switch (type) {
      case "work": return <Target className="h-4 w-4" />;
      case "shortBreak": return <Coffee className="h-4 w-4" />;
      case "longBreak": return <Coffee className="h-4 w-4" />;
      default: return <Target className="h-4 w-4" />;
    }
  };

  const getSessionColor = (type: string) => {
    switch (type) {
      case "work": return "text-red-500 bg-red-500/10 border-red-500/30";
      case "shortBreak": return "text-green-500 bg-green-500/10 border-green-500/30";
      case "longBreak": return "text-blue-500 bg-blue-500/10 border-blue-500/30";
      default: return "text-gray-500 bg-gray-500/10 border-gray-500/30";
    }
  };

  const todaysSessions = completedSessions.filter(
    session => session.completedAt.toDateString() === new Date().toDateString()
  );

  const workSessionsToday = todaysSessions.filter(s => s.type === "work").length;

  return (
    <div className="space-y-6">
      {/* Main Timer */}
      <Card className="vanta-card">
        <CardHeader className="text-center">
          <CardTitle className="flex items-center justify-center gap-2">
            {getSessionIcon(currentSession)}
            <span className="capitalize">{currentSession === "shortBreak" || currentSession === "longBreak" ? "Break Time" : "Focus Time"}</span>
          </CardTitle>
          <Badge variant="outline" className={getSessionColor(currentSession)}>
            {currentSession === "shortBreak" ? "Short Break" : currentSession === "longBreak" ? "Long Break" : "Work Session"}
          </Badge>
        </CardHeader>
        <CardContent className="text-center space-y-6">
          {/* Timer Display */}
          <div className="relative">
            <div className="text-6xl font-mono font-bold text-foreground mb-4">
              {formatTime(timeLeft)}
            </div>
            <Progress value={getProgress()} className="h-2" />
          </div>

          {/* Controls */}
          <div className="flex items-center justify-center gap-4">
            <Button
              onClick={toggleTimer}
              size="lg"
              className={`vanta-button ${isActive ? 'bg-red-500 hover:bg-red-600' : 'bg-green-500 hover:bg-green-600'}`}
            >
              {isActive ? <Pause className="h-5 w-5 mr-2" /> : <Play className="h-5 w-5 mr-2" />}
              {isActive ? "Pause" : "Start"}
            </Button>
            
            <Button onClick={resetTimer} variant="outline" size="lg">
              <RotateCcw className="h-5 w-5 mr-2" />
              Reset
            </Button>
            
            <Button onClick={() => setShowSettings(!showSettings)} variant="outline" size="lg">
              <Settings className="h-5 w-5" />
            </Button>
          </div>

          {/* Session Switcher */}
          <div className="flex items-center justify-center gap-2">
            <Button
              variant={currentSession === "work" ? "default" : "outline"}
              size="sm"
              onClick={() => switchSession("work")}
              className="text-xs"
            >
              Work
            </Button>
            <Button
              variant={currentSession === "shortBreak" ? "default" : "outline"}
              size="sm"
              onClick={() => switchSession("shortBreak")}
              className="text-xs"
            >
              Short Break
            </Button>
            <Button
              variant={currentSession === "longBreak" ? "default" : "outline"}
              size="sm"
              onClick={() => switchSession("longBreak")}
              className="text-xs"
            >
              Long Break
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Settings */}
      {showSettings && (
        <Card className="vanta-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Timer Settings
            </CardTitle>
          </CardHeader>
          <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground mb-2 block">
                Work Duration (minutes)
              </label>
              <Select value={workDuration.toString()} onValueChange={(value) => setWorkDuration(Number(value))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {[15, 20, 25, 30, 45, 60].map(duration => (
                    <SelectItem key={duration} value={duration.toString()}>
                      {duration} minutes
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <label className="text-sm font-medium text-muted-foreground mb-2 block">
                Short Break (minutes)
              </label>
              <Select value={shortBreakDuration.toString()} onValueChange={(value) => setShortBreakDuration(Number(value))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {[3, 5, 10, 15].map(duration => (
                    <SelectItem key={duration} value={duration.toString()}>
                      {duration} minutes
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <label className="text-sm font-medium text-muted-foreground mb-2 block">
                Long Break (minutes)
              </label>
              <Select value={longBreakDuration.toString()} onValueChange={(value) => setLongBreakDuration(Number(value))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {[15, 20, 25, 30].map(duration => (
                    <SelectItem key={duration} value={duration.toString()}>
                      {duration} minutes
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Today's Stats */}
      <Card className="vanta-card">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Today's Progress
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-red-500">{workSessionsToday}</p>
              <p className="text-xs text-muted-foreground">Work Sessions</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-green-500">
                {todaysSessions.filter(s => s.type === "shortBreak").length}
              </p>
              <p className="text-xs text-muted-foreground">Short Breaks</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-500">
                {todaysSessions.filter(s => s.type === "longBreak").length}
              </p>
              <p className="text-xs text-muted-foreground">Long Breaks</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-purple-500">
                {Math.round(workSessionsToday * workDuration / 60 * 10) / 10}h
              </p>
              <p className="text-xs text-muted-foreground">Focus Time</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
