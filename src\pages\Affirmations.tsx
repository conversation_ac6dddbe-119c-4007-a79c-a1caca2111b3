import { useState } from "react";
import { Plus, Heart, Star, Shuffle } from "lucide-react";
import Layout from "@/components/Layout";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";

interface Affirmation {
  id: string;
  text: string;
  category: string;
  favorite: boolean;
  custom: boolean;
}

export default function Affirmations() {
  const [affirmations] = useState<Affirmation[]>([
    {
      id: "1",
      text: "I am capable of achieving my goals with patience and persistence.",
      category: "Motivation",
      favorite: true,
      custom: false,
    },
    {
      id: "2",
      text: "Every challenge I face is an opportunity to grow stronger.",
      category: "Strength",
      favorite: false,
      custom: false,
    },
    {
      id: "3",
      text: "I deserve happiness and success in all areas of my life.",
      category: "Self-Love",
      favorite: true,
      custom: false,
    },
    {
      id: "4",
      text: "I am grateful for the beautiful moments in my day.",
      category: "Gratitude",
      favorite: false,
      custom: true,
    },
  ]);

  const [currentAffirmation, setCurrentAffirmation] = useState(affirmations[0]);
  const [showAddForm, setShowAddForm] = useState(false);

  const getRandomAffirmation = () => {
    const randomIndex = Math.floor(Math.random() * affirmations.length);
    setCurrentAffirmation(affirmations[randomIndex]);
  };

  const categories = ["All", "Motivation", "Strength", "Self-Love", "Gratitude"];

  return (
    <Layout>
      <div className="space-y-6 animate-fade-in">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold text-foreground">Affirmations</h1>
          <Button 
            size="sm" 
            className="gap-2"
            onClick={() => setShowAddForm(!showAddForm)}
          >
            <Plus className="h-4 w-4" />
            Add Custom
          </Button>
        </div>

        {/* Today's Affirmation */}
        <Card className="bg-gradient-to-br from-primary/10 to-accent/10 border-primary/20">
          <CardContent className="p-8 text-center">
            <div className="mb-6">
              <Heart className="h-8 w-8 text-primary mx-auto mb-4" />
              <h2 className="text-lg font-semibold text-foreground mb-2">Today's Affirmation</h2>
            </div>
            <blockquote className="text-xl md:text-2xl font-medium text-foreground leading-relaxed mb-6">
              "{currentAffirmation.text}"
            </blockquote>
            <div className="flex items-center justify-center gap-4">
              <span className="px-3 py-1 bg-primary/20 text-primary rounded-full text-sm">
                {currentAffirmation.category}
              </span>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={getRandomAffirmation}
                className="gap-2"
              >
                <Shuffle className="h-4 w-4" />
                New Affirmation
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Add Custom Affirmation Form */}
        {showAddForm && (
          <Card>
            <CardContent className="p-6">
              <h3 className="font-semibold text-foreground mb-4">Add Custom Affirmation</h3>
              <div className="space-y-4">
                <Textarea 
                  placeholder="Write your affirmation..." 
                  className="min-h-[100px]"
                />
                <Input placeholder="Category (e.g., Self-Love, Motivation)" />
                <div className="flex gap-3">
                  <Button className="flex-1">Add Affirmation</Button>
                  <Button 
                    variant="outline" 
                    onClick={() => setShowAddForm(false)}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Categories Filter */}
        <div className="flex flex-wrap gap-2">
          {categories.map(category => (
            <Button 
              key={category}
              variant="outline"
              size="sm"
              className="rounded-full"
            >
              {category}
            </Button>
          ))}
        </div>

        {/* Affirmations Library */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {affirmations.map(affirmation => (
            <Card 
              key={affirmation.id}
              className="hover:shadow-lg transition-shadow cursor-pointer group"
              onClick={() => setCurrentAffirmation(affirmation)}
            >
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-3">
                  <span className="px-2 py-1 bg-accent text-accent-foreground rounded text-xs">
                    {affirmation.category}
                  </span>
                  <div className="flex items-center gap-2">
                    {affirmation.favorite && (
                      <Star className="h-4 w-4 text-yellow-500 fill-current" />
                    )}
                    {affirmation.custom && (
                      <span className="text-xs text-primary">Custom</span>
                    )}
                  </div>
                </div>
                <p className="text-foreground leading-relaxed group-hover:text-primary transition-colors">
                  "{affirmation.text}"
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Stats */}
        <Card>
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <p className="text-2xl font-bold text-primary">{affirmations.length}</p>
                <p className="text-sm text-muted-foreground">Total Affirmations</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-obsidian-accent">
                  {affirmations.filter(a => a.favorite).length}
                </p>
                <p className="text-sm text-muted-foreground">Favorites</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-obsidian-success">
                  {affirmations.filter(a => a.custom).length}
                </p>
                <p className="text-sm text-muted-foreground">Custom</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
}