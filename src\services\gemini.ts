// Gemini AI Service for VANTA Chat

const GEMINI_API_KEY = "AIzaSyBMWX_4HIW6nxKG98YTjYCQaiEgrm3NwdE";
const GEMINI_API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent";

interface GeminiMessage {
  role: "user" | "model";
  parts: { text: string }[];
}

interface GeminiResponse {
  candidates: {
    content: {
      parts: { text: string }[];
    };
  }[];
}

class GeminiService {
  private conversationHistory: GeminiMessage[] = [];

  constructor() {
    // Initialize with therapeutic context
    this.conversationHistory = [
      {
        role: "model",
        parts: [{
          text: `You are VANTA's AI companion - a warm, empathetic, and supportive therapeutic assistant. Your role is to:

1. Provide emotional support and active listening
2. Offer practical coping strategies and mindfulness techniques
3. Help users process their thoughts and feelings
4. Suggest healthy habits and lifestyle improvements
5. Be encouraging and non-judgmental
6. Keep responses concise but meaningful (2-3 sentences max)
7. Ask thoughtful follow-up questions to encourage reflection

Always maintain a calm, professional, and caring tone. You're here to support mental wellness and personal growth. If someone mentions serious mental health concerns, gently suggest they speak with a professional counselor.

Remember: You're a supportive companion, not a replacement for professional therapy.`
        }]
      }
    ];
  }

  async sendMessage(userMessage: string): Promise<string> {
    try {
      // Add user message to history
      this.conversationHistory.push({
        role: "user",
        parts: [{ text: userMessage }]
      });

      const response = await fetch(`${GEMINI_API_URL}?key=${GEMINI_API_KEY}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          contents: this.conversationHistory,
          generationConfig: {
            temperature: 0.7,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 200,
          },
          safetySettings: [
            {
              category: "HARM_CATEGORY_HARASSMENT",
              threshold: "BLOCK_MEDIUM_AND_ABOVE"
            },
            {
              category: "HARM_CATEGORY_HATE_SPEECH",
              threshold: "BLOCK_MEDIUM_AND_ABOVE"
            },
            {
              category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
              threshold: "BLOCK_MEDIUM_AND_ABOVE"
            },
            {
              category: "HARM_CATEGORY_DANGEROUS_CONTENT",
              threshold: "BLOCK_MEDIUM_AND_ABOVE"
            }
          ]
        }),
      });

      if (!response.ok) {
        throw new Error(`Gemini API error: ${response.status}`);
      }

      const data: GeminiResponse = await response.json();
      
      if (!data.candidates || data.candidates.length === 0) {
        throw new Error("No response from Gemini");
      }

      const aiResponse = data.candidates[0].content.parts[0].text;

      // Add AI response to history
      this.conversationHistory.push({
        role: "model",
        parts: [{ text: aiResponse }]
      });

      // Keep conversation history manageable (last 20 messages)
      if (this.conversationHistory.length > 20) {
        this.conversationHistory = [
          this.conversationHistory[0], // Keep system prompt
          ...this.conversationHistory.slice(-19)
        ];
      }

      return aiResponse;
    } catch (error) {
      console.error("Gemini API error:", error);
      return "I'm having trouble connecting right now. Please try again in a moment. In the meantime, remember that you're doing great and every small step forward matters.";
    }
  }

  // Get mood-based suggestions
  async getMoodSuggestion(mood: string): Promise<string> {
    const moodPrompts = {
      anxious: "I'm feeling quite anxious today. Can you help me with some calming techniques?",
      sad: "I'm feeling down and could use some support and encouragement.",
      stressed: "I'm feeling overwhelmed with stress. What are some ways to manage this?",
      angry: "I'm feeling frustrated and angry. How can I process these emotions healthily?",
      tired: "I'm feeling mentally and physically exhausted. What can help me recharge?",
      happy: "I'm having a good day! How can I maintain this positive energy?",
      grateful: "I'm feeling grateful today. How can I cultivate more gratitude in my life?",
      lonely: "I'm feeling lonely and disconnected. Can you help me feel more connected?"
    };

    const prompt = moodPrompts[mood as keyof typeof moodPrompts] || 
                  `I'm feeling ${mood}. Can you provide some supportive guidance?`;

    return await this.sendMessage(prompt);
  }

  // Get habit-breaking support
  async getHabitBreakerSupport(habit: string, streakDays: number): Promise<string> {
    const prompt = `I'm trying to quit ${habit} and I'm currently ${streakDays} days clean. ${
      streakDays === 0 
        ? "I just started my journey and need motivation to begin."
        : streakDays < 7
        ? "I'm in the early days and finding it challenging."
        : streakDays < 30
        ? "I've made good progress but still struggle with cravings."
        : "I've been doing well but want to stay motivated for the long term."
    } Can you provide encouragement and practical tips?`;

    return await this.sendMessage(prompt);
  }

  // Get daily check-in prompts
  getDailyCheckInPrompts(): string[] {
    return [
      "How are you feeling today? What's on your mind?",
      "What's one thing you're grateful for right now?",
      "How did you sleep last night? How is your energy today?",
      "What's been the highlight of your day so far?",
      "Is there anything that's been worrying you lately?",
      "What's one small thing you could do today to take care of yourself?",
      "How are you managing stress in your life right now?",
      "What's something you're looking forward to?",
      "How connected do you feel to the people in your life?",
      "What would make today feel successful for you?"
    ];
  }

  // Get coping strategies
  getCopingStrategies(): { category: string; strategies: string[] }[] {
    return [
      {
        category: "Breathing & Mindfulness",
        strategies: [
          "Try the 4-7-8 breathing technique: inhale for 4, hold for 7, exhale for 8",
          "Practice a 5-minute mindfulness meditation",
          "Do a body scan to notice areas of tension",
          "Focus on your five senses: what can you see, hear, feel, smell, taste?"
        ]
      },
      {
        category: "Physical Movement",
        strategies: [
          "Take a 10-minute walk outside",
          "Do some gentle stretching",
          "Try progressive muscle relaxation",
          "Dance to your favorite song"
        ]
      },
      {
        category: "Mental Reframing",
        strategies: [
          "Write down three things you're grateful for",
          "Challenge negative thoughts with evidence",
          "Practice self-compassion - speak to yourself like a good friend",
          "Focus on what you can control right now"
        ]
      },
      {
        category: "Connection & Support",
        strategies: [
          "Reach out to a trusted friend or family member",
          "Write in a journal about your feelings",
          "Practice loving-kindness meditation",
          "Do something kind for someone else"
        ]
      }
    ];
  }

  // Clear conversation history
  clearHistory(): void {
    this.conversationHistory = [this.conversationHistory[0]]; // Keep only system prompt
  }
}

export const geminiService = new GeminiService();
