import { supabase } from '@/integrations/supabase/client';
import type { Tables, TablesInsert, TablesUpdate } from '@/integrations/supabase/types';
import { errorHandler } from '@/utils/errorHandler';

export type FinanceItem = Tables<'finance_items'>;
export type FinanceItemInsert = TablesInsert<'finance_items'>;
export type FinanceItemUpdate = TablesUpdate<'finance_items'>;

class FinanceService {
  async getFinanceItems(userId: string): Promise<FinanceItem[]> {
    try {
      const { data, error } = await supabase
        .from('finance_items')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        const message = errorHandler.handleSupabaseError(error, 'getFinanceItems');
        throw new Error(message);
      }
      return data || [];
    } catch (error) {
      errorHandler.logError({
        code: 'FINANCE_FETCH_ERROR',
        message: 'Failed to fetch finance items',
        details: error,
        timestamp: new Date(),
        context: 'financeService.getFinanceItems',
        userId
      });
      throw error;
    }
  }

  async getFinanceItemsByType(userId: string, type: 'owed' | 'owing'): Promise<FinanceItem[]> {
    try {
      const { data, error } = await supabase
        .from('finance_items')
        .select('*')
        .eq('user_id', userId)
        .eq('type', type)
        .order('due_date', { ascending: true });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching finance items by type:', error);
      throw new Error('Failed to fetch finance items');
    }
  }

  async getOverdueItems(userId: string): Promise<FinanceItem[]> {
    try {
      const now = new Date().toISOString();
      const { data, error } = await supabase
        .from('finance_items')
        .select('*')
        .eq('user_id', userId)
        .eq('paid', false)
        .lt('due_date', now)
        .order('due_date', { ascending: true });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching overdue items:', error);
      throw new Error('Failed to fetch overdue items');
    }
  }

  async createFinanceItem(item: FinanceItemInsert): Promise<FinanceItem> {
    try {
      const { data, error } = await supabase
        .from('finance_items')
        .insert(item)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating finance item:', error);
      throw new Error('Failed to create finance item');
    }
  }

  async updateFinanceItem(id: string, updates: FinanceItemUpdate): Promise<FinanceItem> {
    try {
      const { data, error } = await supabase
        .from('finance_items')
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error updating finance item:', error);
      throw new Error('Failed to update finance item');
    }
  }

  async deleteFinanceItem(id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('finance_items')
        .delete()
        .eq('id', id);

      if (error) throw error;
    } catch (error) {
      console.error('Error deleting finance item:', error);
      throw new Error('Failed to delete finance item');
    }
  }

  async markAsPaid(id: string): Promise<FinanceItem> {
    try {
      const { data, error } = await supabase
        .from('finance_items')
        .update({ 
          paid: true,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error marking item as paid:', error);
      throw new Error('Failed to mark item as paid');
    }
  }

  async togglePaidStatus(id: string): Promise<FinanceItem> {
    try {
      // First get the current item
      const { data: currentItem, error: fetchError } = await supabase
        .from('finance_items')
        .select('paid')
        .eq('id', id)
        .single();

      if (fetchError) throw fetchError;

      const { data, error } = await supabase
        .from('finance_items')
        .update({ 
          paid: !currentItem.paid,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error toggling paid status:', error);
      throw new Error('Failed to toggle paid status');
    }
  }

  // Get financial statistics
  async getFinanceStats(userId: string): Promise<{
    totalOwed: number;
    totalOwing: number;
    netBalance: number;
    overdueCount: number;
    overdueAmount: number;
    paidThisMonth: number;
  }> {
    try {
      const { data: allItems, error } = await supabase
        .from('finance_items')
        .select('*')
        .eq('user_id', userId);

      if (error) throw error;

      const now = new Date();
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

      const stats = {
        totalOwed: allItems
          .filter(item => item.type === 'owed' && !item.paid)
          .reduce((sum, item) => sum + item.amount, 0),
        
        totalOwing: allItems
          .filter(item => item.type === 'owing' && !item.paid)
          .reduce((sum, item) => sum + item.amount, 0),
        
        netBalance: 0, // Will be calculated below
        
        overdueCount: allItems.filter(item => 
          !item.paid && 
          item.due_date && 
          new Date(item.due_date) < now
        ).length,
        
        overdueAmount: allItems
          .filter(item => 
            !item.paid && 
            item.due_date && 
            new Date(item.due_date) < now
          )
          .reduce((sum, item) => sum + item.amount, 0),
        
        paidThisMonth: allItems
          .filter(item => 
            item.paid && 
            item.updated_at && 
            new Date(item.updated_at) >= startOfMonth
          )
          .reduce((sum, item) => sum + item.amount, 0)
      };

      stats.netBalance = stats.totalOwed - stats.totalOwing;

      return stats;
    } catch (error) {
      console.error('Error fetching finance stats:', error);
      throw new Error('Failed to fetch finance statistics');
    }
  }

  // Get items due soon (within next 7 days)
  async getItemsDueSoon(userId: string): Promise<FinanceItem[]> {
    try {
      const now = new Date();
      const nextWeek = new Date();
      nextWeek.setDate(nextWeek.getDate() + 7);

      const { data, error } = await supabase
        .from('finance_items')
        .select('*')
        .eq('user_id', userId)
        .eq('paid', false)
        .gte('due_date', now.toISOString())
        .lte('due_date', nextWeek.toISOString())
        .order('due_date', { ascending: true });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching items due soon:', error);
      throw new Error('Failed to fetch items due soon');
    }
  }

  // Get monthly spending by category
  async getMonthlySpending(userId: string, year: number, month: number): Promise<Record<string, number>> {
    try {
      const startDate = new Date(year, month - 1, 1);
      const endDate = new Date(year, month, 0);

      const { data, error } = await supabase
        .from('finance_items')
        .select('category, amount')
        .eq('user_id', userId)
        .eq('type', 'owing')
        .eq('paid', true)
        .gte('updated_at', startDate.toISOString())
        .lte('updated_at', endDate.toISOString());

      if (error) throw error;

      const spending: Record<string, number> = {};
      data.forEach(item => {
        spending[item.category] = (spending[item.category] || 0) + item.amount;
      });

      return spending;
    } catch (error) {
      console.error('Error fetching monthly spending:', error);
      throw new Error('Failed to fetch monthly spending');
    }
  }
}

export const financeService = new FinanceService();
