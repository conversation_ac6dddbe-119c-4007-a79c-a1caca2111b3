-- Database Functions and Triggers for <PERSON><PERSON> App

-- Function to handle updated_at timestamps
CREATE OR REPLACE FUNCTION handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to create user profile on signup
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (id, email, full_name)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email)
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to auto-complete task when all subtasks are done
CREATE OR REPLACE FUNCTION check_task_completion()
RETURNS TRIGGER AS $$
DECLARE
    total_subtasks INTEGER;
    completed_subtasks INTEGER;
BEGIN
    -- Count total and completed subtasks for the task
    SELECT COUNT(*) INTO total_subtasks
    FROM subtasks
    WHERE task_id = COALESCE(NEW.task_id, OLD.task_id);
    
    SELECT COUNT(*) INTO completed_subtasks
    FROM subtasks
    WHERE task_id = COALESCE(NEW.task_id, OLD.task_id)
    AND completed = TRUE;
    
    -- If all subtasks are completed and there are subtasks, mark task as completed
    IF total_subtasks > 0 AND completed_subtasks = total_subtasks THEN
        UPDATE tasks
        SET status = 'completed', completed_at = NOW()
        WHERE id = COALESCE(NEW.task_id, OLD.task_id)
        AND status != 'completed';
    -- If not all subtasks are completed, ensure task is not marked as completed
    ELSIF total_subtasks > 0 AND completed_subtasks < total_subtasks THEN
        UPDATE tasks
        SET status = 'pending', completed_at = NULL
        WHERE id = COALESCE(NEW.task_id, OLD.task_id)
        AND status = 'completed';
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to calculate habit streak
CREATE OR REPLACE FUNCTION calculate_habit_streak(habit_uuid UUID)
RETURNS INTEGER AS $$
DECLARE
    streak_count INTEGER := 0;
    current_date_check DATE := CURRENT_DATE;
    entry_exists BOOLEAN;
BEGIN
    LOOP
        -- Check if there's a completed entry for the current date
        SELECT EXISTS(
            SELECT 1 FROM habit_entries
            WHERE habit_id = habit_uuid
            AND date = current_date_check
            AND completed = TRUE
        ) INTO entry_exists;
        
        -- If no completed entry found, break the loop
        IF NOT entry_exists THEN
            EXIT;
        END IF;
        
        -- Increment streak and move to previous day
        streak_count := streak_count + 1;
        current_date_check := current_date_check - INTERVAL '1 day';
    END LOOP;
    
    RETURN streak_count;
END;
$$ LANGUAGE plpgsql;

-- Function to get user's productivity stats
CREATE OR REPLACE FUNCTION get_productivity_stats(user_uuid UUID, days_back INTEGER DEFAULT 7)
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    SELECT json_build_object(
        'completed_tasks', (
            SELECT COUNT(*)
            FROM tasks
            WHERE user_id = user_uuid
            AND status = 'completed'
            AND completed_at >= CURRENT_DATE - INTERVAL '1 day' * days_back
        ),
        'total_tasks', (
            SELECT COUNT(*)
            FROM tasks
            WHERE user_id = user_uuid
            AND created_at >= CURRENT_DATE - INTERVAL '1 day' * days_back
        ),
        'pomodoro_sessions', (
            SELECT COUNT(*)
            FROM pomodoro_sessions
            WHERE user_id = user_uuid
            AND completed = TRUE
            AND date >= CURRENT_DATE - INTERVAL '1 day' * days_back
        ),
        'habit_completion_rate', (
            SELECT ROUND(
                (COUNT(CASE WHEN completed THEN 1 END)::DECIMAL / NULLIF(COUNT(*), 0)) * 100,
                2
            )
            FROM habit_entries he
            JOIN habits h ON h.id = he.habit_id
            WHERE h.user_id = user_uuid
            AND he.date >= CURRENT_DATE - INTERVAL '1 day' * days_back
        ),
        'mood_average', (
            SELECT ROUND(AVG(intensity), 1)
            FROM mood_entries
            WHERE user_id = user_uuid
            AND date >= CURRENT_DATE - INTERVAL '1 day' * days_back
        )
    ) INTO result;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to clean up old data (for privacy and performance)
CREATE OR REPLACE FUNCTION cleanup_old_data()
RETURNS VOID AS $$
BEGIN
    -- Delete chat messages older than 6 months
    DELETE FROM chat_messages
    WHERE created_at < NOW() - INTERVAL '6 months';
    
    -- Delete completed tasks older than 1 year
    DELETE FROM tasks
    WHERE status = 'completed'
    AND completed_at < NOW() - INTERVAL '1 year';
    
    -- Delete old pomodoro sessions (keep 1 year)
    DELETE FROM pomodoro_sessions
    WHERE created_at < NOW() - INTERVAL '1 year';
    
    -- Delete old mood entries (keep 2 years)
    DELETE FROM mood_entries
    WHERE created_at < NOW() - INTERVAL '2 years';
    
    RAISE NOTICE 'Old data cleanup completed';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create triggers for updated_at timestamps
CREATE TRIGGER set_updated_at_profiles
    BEFORE UPDATE ON profiles
    FOR EACH ROW
    EXECUTE FUNCTION handle_updated_at();

CREATE TRIGGER set_updated_at_tasks
    BEFORE UPDATE ON tasks
    FOR EACH ROW
    EXECUTE FUNCTION handle_updated_at();

CREATE TRIGGER set_updated_at_finance_items
    BEFORE UPDATE ON finance_items
    FOR EACH ROW
    EXECUTE FUNCTION handle_updated_at();

CREATE TRIGGER set_updated_at_shopping_lists
    BEFORE UPDATE ON shopping_lists
    FOR EACH ROW
    EXECUTE FUNCTION handle_updated_at();

CREATE TRIGGER set_updated_at_wishlist_items
    BEFORE UPDATE ON wishlist_items
    FOR EACH ROW
    EXECUTE FUNCTION handle_updated_at();

CREATE TRIGGER set_updated_at_habits
    BEFORE UPDATE ON habits
    FOR EACH ROW
    EXECUTE FUNCTION handle_updated_at();

CREATE TRIGGER set_updated_at_calendar_events
    BEFORE UPDATE ON calendar_events
    FOR EACH ROW
    EXECUTE FUNCTION handle_updated_at();

CREATE TRIGGER set_updated_at_notes
    BEFORE UPDATE ON notes
    FOR EACH ROW
    EXECUTE FUNCTION handle_updated_at();

-- Create trigger for new user profile creation
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION handle_new_user();

-- Create triggers for task completion checking
CREATE TRIGGER check_task_completion_on_subtask_change
    AFTER INSERT OR UPDATE OR DELETE ON subtasks
    FOR EACH ROW
    EXECUTE FUNCTION check_task_completion();

-- Create a scheduled job to run cleanup (requires pg_cron extension)
-- This would be set up in production with proper scheduling
-- SELECT cron.schedule('cleanup-old-data', '0 2 * * 0', 'SELECT cleanup_old_data();');

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION calculate_habit_streak(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_productivity_stats(UUID, INTEGER) TO authenticated;

-- Create views for common queries
CREATE VIEW user_task_summary AS
SELECT 
    user_id,
    COUNT(*) as total_tasks,
    COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_tasks,
    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_tasks,
    COUNT(CASE WHEN priority = 'high' THEN 1 END) as high_priority_tasks,
    COUNT(CASE WHEN starred THEN 1 END) as starred_tasks
FROM tasks
WHERE archived = FALSE
GROUP BY user_id;

CREATE VIEW user_habit_streaks AS
SELECT 
    h.user_id,
    h.id as habit_id,
    h.name as habit_name,
    calculate_habit_streak(h.id) as current_streak
FROM habits h
WHERE h.archived = FALSE;

-- Grant access to views
GRANT SELECT ON user_task_summary TO authenticated;
GRANT SELECT ON user_habit_streaks TO authenticated;
