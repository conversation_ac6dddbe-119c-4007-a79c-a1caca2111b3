import { supabase } from '@/integrations/supabase/client';
import type { Tables, TablesInsert, TablesUpdate } from '@/integrations/supabase/types';
import { errorHandler } from '@/utils/errorHandler';

export type CalendarEvent = Tables<'calendar_events'>;
export type CalendarEventInsert = TablesInsert<'calendar_events'>;
export type CalendarEventUpdate = TablesUpdate<'calendar_events'>;

class CalendarService {
  async getEvents(userId: string): Promise<CalendarEvent[]> {
    try {
      const { data, error } = await supabase
        .from('calendar_events')
        .select('*')
        .eq('user_id', userId)
        .order('start_date', { ascending: true });

      if (error) {
        const message = errorHandler.handleSupabaseError(error, 'getEvents');
        throw new Error(message);
      }
      return data || [];
    } catch (error) {
      errorHandler.logError({
        code: 'CALENDAR_FETCH_ERROR',
        message: 'Failed to fetch calendar events',
        details: error,
        timestamp: new Date(),
        context: 'calendarService.getEvents',
        userId
      });
      throw error;
    }
  }

  async getEventsByDateRange(userId: string, startDate: Date, endDate: Date): Promise<CalendarEvent[]> {
    try {
      const { data, error } = await supabase
        .from('calendar_events')
        .select('*')
        .eq('user_id', userId)
        .gte('start_date', startDate.toISOString())
        .lte('start_date', endDate.toISOString())
        .order('start_date', { ascending: true });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching events by date range:', error);
      throw new Error('Failed to fetch events for date range');
    }
  }

  async createEvent(event: CalendarEventInsert): Promise<CalendarEvent> {
    try {
      const { data, error } = await supabase
        .from('calendar_events')
        .insert(event)
        .select()
        .single();

      if (error) {
        const message = errorHandler.handleSupabaseError(error, 'createEvent');
        throw new Error(message);
      }
      return data;
    } catch (error) {
      errorHandler.logError({
        code: 'CALENDAR_CREATE_ERROR',
        message: 'Failed to create calendar event',
        details: error,
        timestamp: new Date(),
        context: 'calendarService.createEvent'
      });
      throw error;
    }
  }

  async updateEvent(id: string, updates: CalendarEventUpdate): Promise<CalendarEvent> {
    try {
      const { data, error } = await supabase
        .from('calendar_events')
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error updating calendar event:', error);
      throw new Error('Failed to update calendar event');
    }
  }

  async deleteEvent(id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('calendar_events')
        .delete()
        .eq('id', id);

      if (error) throw error;
    } catch (error) {
      console.error('Error deleting calendar event:', error);
      throw new Error('Failed to delete calendar event');
    }
  }

  async getUpcomingEvents(userId: string, limit: number = 5): Promise<CalendarEvent[]> {
    try {
      const now = new Date();
      const { data, error } = await supabase
        .from('calendar_events')
        .select('*')
        .eq('user_id', userId)
        .gte('start_date', now.toISOString())
        .order('start_date', { ascending: true })
        .limit(limit);

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching upcoming events:', error);
      throw new Error('Failed to fetch upcoming events');
    }
  }

  async getTodaysEvents(userId: string): Promise<CalendarEvent[]> {
    try {
      const today = new Date();
      const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
      const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59);

      const { data, error } = await supabase
        .from('calendar_events')
        .select('*')
        .eq('user_id', userId)
        .gte('start_date', startOfDay.toISOString())
        .lte('start_date', endOfDay.toISOString())
        .order('start_date', { ascending: true });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching today\'s events:', error);
      throw new Error('Failed to fetch today\'s events');
    }
  }
}

export const calendarService = new CalendarService();
