export interface CurrencyConfig {
  code: string;
  symbol: string;
  name: string;
  locale: string;
  position: 'before' | 'after';
}

export const SUPPORTED_CURRENCIES: Record<string, CurrencyConfig> = {
  ZAR: {
    code: 'ZAR',
    symbol: 'R',
    name: 'South African Rand',
    locale: 'en-ZA',
    position: 'before'
  },
  USD: {
    code: 'USD',
    symbol: '$',
    name: 'US Dollar',
    locale: 'en-US',
    position: 'before'
  },
  EUR: {
    code: 'EUR',
    symbol: '€',
    name: 'Euro',
    locale: 'en-EU',
    position: 'before'
  },
  GBP: {
    code: 'GBP',
    symbol: '£',
    name: 'British Pound',
    locale: 'en-GB',
    position: 'before'
  },
  JPY: {
    code: 'JPY',
    symbol: '¥',
    name: 'Japanese Yen',
    locale: 'ja-JP',
    position: 'before'
  }
};

class CurrencyService {
  private currentCurrency: string = 'ZAR';

  constructor() {
    // Initialize from environment or localStorage
    this.currentCurrency = this.loadCurrencyFromStorage();
  }

  private loadCurrencyFromStorage(): string {
    try {
      // Try localStorage first
      const stored = localStorage.getItem('vanta-currency');
      if (stored && SUPPORTED_CURRENCIES[stored]) {
        return stored;
      }
      
      // Fallback to environment variable
      const defaultCurrency = import.meta.env.VITE_DEFAULT_CURRENCY || 'ZAR';
      if (SUPPORTED_CURRENCIES[defaultCurrency]) {
        return defaultCurrency;
      }
      
      return 'ZAR';
    } catch (error) {
      console.warn('Failed to load currency from storage:', error);
      return 'ZAR';
    }
  }

  getCurrentCurrency(): string {
    return this.currentCurrency;
  }

  setCurrency(currencyCode: string): void {
    if (!SUPPORTED_CURRENCIES[currencyCode]) {
      throw new Error(`Unsupported currency: ${currencyCode}`);
    }
    
    this.currentCurrency = currencyCode;
    
    try {
      localStorage.setItem('vanta-currency', currencyCode);
    } catch (error) {
      console.warn('Failed to save currency to storage:', error);
    }
  }

  getCurrencyConfig(currencyCode?: string): CurrencyConfig {
    const code = currencyCode || this.currentCurrency;
    return SUPPORTED_CURRENCIES[code] || SUPPORTED_CURRENCIES.ZAR;
  }

  formatAmount(amount: number, currencyCode?: string, options?: {
    showSymbol?: boolean;
    showCode?: boolean;
    decimals?: number;
    compact?: boolean;
  }): string {
    const {
      showSymbol = true,
      showCode = false,
      decimals = 2,
      compact = false
    } = options || {};

    const config = this.getCurrencyConfig(currencyCode);
    
    // Format the number
    let formattedAmount: string;
    
    if (compact && Math.abs(amount) >= 1000) {
      // Use compact notation for large numbers
      formattedAmount = new Intl.NumberFormat(config.locale, {
        notation: 'compact',
        maximumFractionDigits: 1
      }).format(amount);
    } else {
      formattedAmount = new Intl.NumberFormat(config.locale, {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
      }).format(amount);
    }

    // Build the final string
    let result = '';
    
    if (showSymbol && config.position === 'before') {
      result += config.symbol;
    }
    
    result += formattedAmount;
    
    if (showSymbol && config.position === 'after') {
      result += config.symbol;
    }
    
    if (showCode) {
      result += ` ${config.code}`;
    }
    
    return result;
  }

  // Format for display in UI components
  formatForDisplay(amount: number, options?: {
    compact?: boolean;
    showCode?: boolean;
  }): string {
    return this.formatAmount(amount, undefined, {
      showSymbol: true,
      showCode: options?.showCode || false,
      decimals: 2,
      compact: options?.compact || false
    });
  }

  // Format for financial calculations (no symbols, just numbers)
  formatForCalculation(amount: number): string {
    return this.formatAmount(amount, undefined, {
      showSymbol: false,
      showCode: false,
      decimals: 2
    });
  }

  // Parse currency string back to number
  parseAmount(currencyString: string): number {
    try {
      // Remove all non-numeric characters except decimal point and minus
      const cleanString = currencyString.replace(/[^\d.-]/g, '');
      const parsed = parseFloat(cleanString);
      return isNaN(parsed) ? 0 : parsed;
    } catch (error) {
      console.warn('Failed to parse currency string:', currencyString, error);
      return 0;
    }
  }

  // Get all supported currencies for dropdowns
  getSupportedCurrencies(): CurrencyConfig[] {
    return Object.values(SUPPORTED_CURRENCIES);
  }

  // Convert between currencies (would need exchange rate API in real app)
  convertCurrency(amount: number, fromCurrency: string, toCurrency: string): number {
    // For now, return the same amount since we don't have exchange rates
    // In a real app, you'd integrate with an exchange rate API
    console.warn('Currency conversion not implemented - returning original amount');
    return amount;
  }

  // Get currency symbol only
  getSymbol(currencyCode?: string): string {
    return this.getCurrencyConfig(currencyCode).symbol;
  }

  // Get currency name
  getName(currencyCode?: string): string {
    return this.getCurrencyConfig(currencyCode).name;
  }

  // Check if currency is supported
  isSupported(currencyCode: string): boolean {
    return !!SUPPORTED_CURRENCIES[currencyCode];
  }
}

// Export singleton instance
export const currencyService = new CurrencyService();

// Export utility functions for direct use
export const formatCurrency = (amount: number, options?: Parameters<CurrencyService['formatForDisplay']>[1]) => 
  currencyService.formatForDisplay(amount, options);

export const getCurrencySymbol = (currencyCode?: string) => 
  currencyService.getSymbol(currencyCode);

export const parseCurrencyAmount = (currencyString: string) => 
  currencyService.parseAmount(currencyString);
