import { useState } from "react";
import { Plus, Check, X, Target, TrendingUp, Calendar, Flame, Award } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { format, startOfWeek, addDays, isSameDay, subDays } from "date-fns";

interface HabitEntry {
  id: string;
  habitId: string;
  date: Date;
  completed: boolean;
  notes?: string;
}

interface Habit {
  id: string;
  name: string;
  description?: string;
  category: string;
  color: string;
  icon: string;
  targetFrequency: "daily" | "weekly";
  targetCount: number;
  createdAt: Date;
  archived: boolean;
}

export default function HabitTracker() {
  const [habits] = useState<Habit[]>([
    {
      id: "1",
      name: "Drink Water",
      description: "Stay hydrated throughout the day",
      category: "Health",
      color: "bg-blue-500",
      icon: "💧",
      targetFrequency: "daily",
      targetCount: 8,
      createdAt: new Date(2024, 11, 1),
      archived: false
    },
    {
      id: "2",
      name: "Exercise",
      description: "30 minutes of physical activity",
      category: "Fitness",
      color: "bg-green-500",
      icon: "🏃‍♂️",
      targetFrequency: "daily",
      targetCount: 1,
      createdAt: new Date(2024, 11, 1),
      archived: false
    },
    {
      id: "3",
      name: "Read",
      description: "Read for at least 20 minutes",
      category: "Learning",
      color: "bg-purple-500",
      icon: "📚",
      targetFrequency: "daily",
      targetCount: 1,
      createdAt: new Date(2024, 11, 1),
      archived: false
    },
    {
      id: "4",
      name: "Meditate",
      description: "Mindfulness practice",
      category: "Wellness",
      color: "bg-indigo-500",
      icon: "🧘‍♀️",
      targetFrequency: "daily",
      targetCount: 1,
      createdAt: new Date(2024, 11, 1),
      archived: false
    },
    {
      id: "5",
      name: "Journal",
      description: "Write down thoughts and reflections",
      category: "Personal",
      color: "bg-yellow-500",
      icon: "✍️",
      targetFrequency: "daily",
      targetCount: 1,
      createdAt: new Date(2024, 11, 1),
      archived: false
    }
  ]);

  const [entries, setEntries] = useState<HabitEntry[]>([
    // Mock some entries for the past week
    { id: "1", habitId: "1", date: new Date(), completed: true },
    { id: "2", habitId: "2", date: new Date(), completed: true },
    { id: "3", habitId: "3", date: new Date(), completed: false },
    { id: "4", habitId: "1", date: subDays(new Date(), 1), completed: true },
    { id: "5", habitId: "2", date: subDays(new Date(), 1), completed: false },
    { id: "6", habitId: "4", date: subDays(new Date(), 1), completed: true },
  ]);

  const [selectedDate] = useState(new Date());

  const toggleHabit = (habitId: string, date: Date) => {
    const existingEntry = entries.find(entry => 
      entry.habitId === habitId && isSameDay(entry.date, date)
    );

    if (existingEntry) {
      setEntries(prev => prev.map(entry =>
        entry.id === existingEntry.id 
          ? { ...entry, completed: !entry.completed }
          : entry
      ));
    } else {
      const newEntry: HabitEntry = {
        id: Date.now().toString(),
        habitId,
        date,
        completed: true
      };
      setEntries(prev => [...prev, newEntry]);
    }
  };

  const getHabitStatus = (habitId: string, date: Date) => {
    const entry = entries.find(entry => 
      entry.habitId === habitId && isSameDay(entry.date, date)
    );
    return entry?.completed || false;
  };

  const getWeekDays = () => {
    const start = startOfWeek(selectedDate);
    return Array.from({ length: 7 }, (_, i) => addDays(start, i));
  };

  const getHabitStreak = (habitId: string) => {
    let streak = 0;
    let currentDate = new Date();
    
    while (true) {
      const entry = entries.find(entry => 
        entry.habitId === habitId && isSameDay(entry.date, currentDate)
      );
      
      if (entry?.completed) {
        streak++;
        currentDate = subDays(currentDate, 1);
      } else {
        break;
      }
    }
    
    return streak;
  };

  const getCompletionRate = (habitId: string, days: number = 7) => {
    const recentDays = Array.from({ length: days }, (_, i) => subDays(new Date(), i));
    const completedDays = recentDays.filter(date => getHabitStatus(habitId, date)).length;
    return Math.round((completedDays / days) * 100);
  };

  const getTotalCompletedToday = () => {
    return habits.filter(habit => getHabitStatus(habit.id, new Date())).length;
  };

  const weekDays = getWeekDays();

  return (
    <div className="space-y-6">
      {/* Today's Overview */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="vanta-card p-4 bg-gradient-to-br from-green-500/10 to-emerald-500/5 border-green-500/20">
          <div className="flex items-center gap-3">
            <Target className="h-5 w-5 text-green-500" />
            <div>
              <p className="text-lg font-bold text-green-500">{getTotalCompletedToday()}</p>
              <p className="text-xs text-muted-foreground">Completed Today</p>
            </div>
          </div>
        </div>
        
        <div className="vanta-card p-4 bg-gradient-to-br from-blue-500/10 to-cyan-500/5 border-blue-500/20">
          <div className="flex items-center gap-3">
            <Calendar className="h-5 w-5 text-blue-500" />
            <div>
              <p className="text-lg font-bold text-blue-500">{habits.length}</p>
              <p className="text-xs text-muted-foreground">Active Habits</p>
            </div>
          </div>
        </div>
        
        <div className="vanta-card p-4 bg-gradient-to-br from-orange-500/10 to-red-500/5 border-orange-500/20">
          <div className="flex items-center gap-3">
            <Flame className="h-5 w-5 text-orange-500" />
            <div>
              <p className="text-lg font-bold text-orange-500">
                {Math.max(...habits.map(habit => getHabitStreak(habit.id)))}
              </p>
              <p className="text-xs text-muted-foreground">Best Streak</p>
            </div>
          </div>
        </div>
        
        <div className="vanta-card p-4 bg-gradient-to-br from-purple-500/10 to-pink-500/5 border-purple-500/20">
          <div className="flex items-center gap-3">
            <Award className="h-5 w-5 text-purple-500" />
            <div>
              <p className="text-lg font-bold text-purple-500">
                {Math.round(habits.reduce((sum, habit) => sum + getCompletionRate(habit.id), 0) / habits.length)}%
              </p>
              <p className="text-xs text-muted-foreground">Avg Completion</p>
            </div>
          </div>
        </div>
      </div>

      {/* Habit Grid */}
      <Card className="vanta-card">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Habit Tracker</span>
            <Button size="sm" className="gap-2 vanta-button">
              <Plus className="h-4 w-4" />
              Add Habit
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Week Header */}
          <div className="grid grid-cols-8 gap-2 mb-4">
            <div className="text-sm font-medium text-muted-foreground">Habit</div>
            {weekDays.map(day => (
              <div key={day.toISOString()} className="text-center">
                <div className="text-xs text-muted-foreground">{format(day, "EEE")}</div>
                <div className="text-sm font-medium">{format(day, "d")}</div>
              </div>
            ))}
          </div>

          {/* Habits */}
          <div className="space-y-3">
            {habits.filter(habit => !habit.archived).map(habit => (
              <div key={habit.id} className="grid grid-cols-8 gap-2 items-center p-3 rounded-lg bg-white/5 hover:bg-white/10 transition-colors">
                <div className="flex items-center gap-3">
                  <span className="text-lg">{habit.icon}</span>
                  <div>
                    <p className="font-medium text-sm">{habit.name}</p>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="text-xs">
                        {habit.category}
                      </Badge>
                      <div className="flex items-center gap-1">
                        <Flame className="h-3 w-3 text-orange-500" />
                        <span className="text-xs text-muted-foreground">
                          {getHabitStreak(habit.id)}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                
                {weekDays.map(day => {
                  const isCompleted = getHabitStatus(habit.id, day);
                  const isToday = isSameDay(day, new Date());
                  const isFuture = day > new Date();
                  
                  return (
                    <div key={day.toISOString()} className="flex justify-center">
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => !isFuture && toggleHabit(habit.id, day)}
                        disabled={isFuture}
                        className={`h-8 w-8 p-0 rounded-full transition-all duration-200 ${
                          isCompleted 
                            ? `${habit.color} text-white hover:opacity-80` 
                            : isToday
                            ? "border-2 border-primary hover:bg-primary/20"
                            : "hover:bg-white/10"
                        } ${isFuture ? "opacity-30" : ""}`}
                      >
                        {isCompleted ? (
                          <Check className="h-4 w-4" />
                        ) : (
                          <div className="w-2 h-2 rounded-full bg-current opacity-30" />
                        )}
                      </Button>
                    </div>
                  );
                })}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Progress Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="vanta-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Weekly Progress
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {habits.slice(0, 5).map(habit => {
              const completionRate = getCompletionRate(habit.id, 7);
              return (
                <div key={habit.id} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="text-sm">{habit.icon}</span>
                      <span className="text-sm font-medium">{habit.name}</span>
                    </div>
                    <span className="text-sm text-muted-foreground">{completionRate}%</span>
                  </div>
                  <Progress value={completionRate} className="h-2" />
                </div>
              );
            })}
          </CardContent>
        </Card>

        <Card className="vanta-card">
          <CardHeader>
            <CardTitle>Habit Categories</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {["Health", "Fitness", "Learning", "Wellness", "Personal"].map(category => {
                const categoryHabits = habits.filter(habit => habit.category === category);
                const completedToday = categoryHabits.filter(habit => getHabitStatus(habit.id, new Date())).length;
                
                return categoryHabits.length > 0 ? (
                  <div key={category} className="flex items-center justify-between">
                    <span className="text-sm font-medium">{category}</span>
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-muted-foreground">
                        {completedToday}/{categoryHabits.length}
                      </span>
                      <div className="w-16 bg-muted rounded-full h-2">
                        <div 
                          className="h-2 bg-primary rounded-full transition-all duration-300"
                          style={{ width: `${(completedToday / categoryHabits.length) * 100}%` }}
                        />
                      </div>
                    </div>
                  </div>
                ) : null;
              })}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
