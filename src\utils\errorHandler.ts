import { toast } from "@/hooks/use-toast";

export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: Date;
  userId?: string;
  context?: string;
}

export class ErrorHandler {
  private static instance: <PERSON>rrorHandler;
  private errorLog: AppError[] = [];

  private constructor() {
    this.setupGlobalErrorHandlers();
  }

  static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }

  private setupGlobalErrorHandlers() {
    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      console.error('Unhandled promise rejection:', event.reason);
      this.logError({
        code: 'UNHANDLED_PROMISE_REJECTION',
        message: event.reason?.message || 'Unhandled promise rejection',
        details: event.reason,
        timestamp: new Date(),
        context: 'global'
      });
      
      // Show user-friendly error
      toast({
        title: "Something went wrong",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
    });

    // Handle JavaScript errors
    window.addEventListener('error', (event) => {
      console.error('JavaScript error:', event.error);
      this.logError({
        code: 'JAVASCRIPT_ERROR',
        message: event.error?.message || event.message || 'JavaScript error',
        details: {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          stack: event.error?.stack
        },
        timestamp: new Date(),
        context: 'global'
      });
    });
  }

  logError(error: AppError) {
    this.errorLog.push(error);
    
    // Keep only last 100 errors to prevent memory issues
    if (this.errorLog.length > 100) {
      this.errorLog = this.errorLog.slice(-100);
    }

    // In production, you might want to send errors to a logging service
    if (import.meta.env.VITE_APP_ENVIRONMENT === 'production') {
      this.sendToLoggingService(error);
    }
  }

  private async sendToLoggingService(error: AppError) {
    try {
      // Example: Send to Sentry, LogRocket, or your own logging service
      // await fetch('/api/errors', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(error)
      // });
      console.log('Error logged:', error);
    } catch (loggingError) {
      console.error('Failed to log error to service:', loggingError);
    }
  }

  getErrorLog(): AppError[] {
    return [...this.errorLog];
  }

  clearErrorLog() {
    this.errorLog = [];
  }

  // Handle specific error types
  handleSupabaseError(error: any, context: string = 'supabase'): string {
    let userMessage = 'An error occurred. Please try again.';
    
    if (error?.code) {
      switch (error.code) {
        case 'PGRST116':
          userMessage = 'No data found.';
          break;
        case 'PGRST301':
          userMessage = 'You do not have permission to perform this action.';
          break;
        case '23505':
          userMessage = 'This item already exists.';
          break;
        case '23503':
          userMessage = 'Cannot delete this item because it is referenced by other data.';
          break;
        case 'auth/invalid-email':
          userMessage = 'Please enter a valid email address.';
          break;
        case 'auth/user-not-found':
          userMessage = 'No account found with this email address.';
          break;
        case 'auth/wrong-password':
          userMessage = 'Incorrect password. Please try again.';
          break;
        case 'auth/too-many-requests':
          userMessage = 'Too many failed attempts. Please try again later.';
          break;
        default:
          userMessage = error.message || userMessage;
      }
    } else if (error?.message) {
      userMessage = error.message;
    }

    this.logError({
      code: error?.code || 'SUPABASE_ERROR',
      message: error?.message || 'Supabase error',
      details: error,
      timestamp: new Date(),
      context
    });

    return userMessage;
  }

  handleNetworkError(error: any, context: string = 'network'): string {
    let userMessage = 'Network error. Please check your connection and try again.';

    if (error?.code === 'NETWORK_ERROR' || !navigator.onLine) {
      userMessage = 'You appear to be offline. Please check your internet connection.';
    } else if (error?.status) {
      switch (error.status) {
        case 400:
          userMessage = 'Invalid request. Please check your input and try again.';
          break;
        case 401:
          userMessage = 'You need to log in to perform this action.';
          break;
        case 403:
          userMessage = 'You do not have permission to perform this action.';
          break;
        case 404:
          userMessage = 'The requested resource was not found.';
          break;
        case 429:
          userMessage = 'Too many requests. Please wait a moment and try again.';
          break;
        case 500:
          userMessage = 'Server error. Please try again later.';
          break;
        case 503:
          userMessage = 'Service temporarily unavailable. Please try again later.';
          break;
        default:
          userMessage = `Network error (${error.status}). Please try again.`;
      }
    }

    this.logError({
      code: `NETWORK_ERROR_${error?.status || 'UNKNOWN'}`,
      message: error?.message || 'Network error',
      details: error,
      timestamp: new Date(),
      context
    });

    return userMessage;
  }

  handleValidationError(error: any, context: string = 'validation'): string {
    let userMessage = 'Please check your input and try again.';

    if (error?.issues && Array.isArray(error.issues)) {
      // Handle Zod validation errors
      userMessage = error.issues.map((issue: any) => issue.message).join(', ');
    } else if (error?.message) {
      userMessage = error.message;
    }

    this.logError({
      code: 'VALIDATION_ERROR',
      message: error?.message || 'Validation error',
      details: error,
      timestamp: new Date(),
      context
    });

    return userMessage;
  }

  // Utility function to show error toast
  showErrorToast(error: any, context: string = 'general', title: string = 'Error') {
    let message: string;

    if (typeof error === 'string') {
      message = error;
    } else if (error?.code?.startsWith('PGRST') || error?.code?.startsWith('auth/')) {
      message = this.handleSupabaseError(error, context);
    } else if (error?.status || error?.code === 'NETWORK_ERROR') {
      message = this.handleNetworkError(error, context);
    } else if (error?.issues) {
      message = this.handleValidationError(error, context);
    } else {
      message = error?.message || 'An unexpected error occurred. Please try again.';
      this.logError({
        code: 'UNKNOWN_ERROR',
        message: message,
        details: error,
        timestamp: new Date(),
        context
      });
    }

    toast({
      title,
      description: message,
      variant: "destructive",
    });
  }

  // Utility function to show success toast
  showSuccessToast(message: string, title: string = 'Success') {
    toast({
      title,
      description: message,
    });
  }

  // Check if user is online
  isOnline(): boolean {
    return navigator.onLine;
  }

  // Retry function with exponential backoff
  async retry<T>(
    fn: () => Promise<T>,
    maxAttempts: number = 3,
    baseDelay: number = 1000
  ): Promise<T> {
    let lastError: any;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error;
        
        if (attempt === maxAttempts) {
          break;
        }

        // Exponential backoff
        const delay = baseDelay * Math.pow(2, attempt - 1);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw lastError;
  }
}

// Export singleton instance
export const errorHandler = ErrorHandler.getInstance();
