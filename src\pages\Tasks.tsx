import { useState } from "react";
import { Plus, Search, CheckSquare, Square, Trash2, Edit, Folder, Calendar, Clock } from "lucide-react";
import Layout from "@/components/Layout";
import AuthGuard from "@/components/AuthGuard";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { format, isToday, isTomorrow, addDays } from "date-fns";

interface Task {
  id: string;
  title: string;
  completed: boolean;
  folder: string;
  dueDate?: Date;
  isTimeSpecific: boolean;
  createdAt: Date;
}

export default function Tasks() {
  const [tasks, setTasks] = useState<Task[]>([
    // Empty by default - ready for production use
  ]);

  const [newTask, setNewTask] = useState("");
  const [newTaskFolder, setNewTaskFolder] = useState("Personal");
  const [newTaskDueDate, setNewTaskDueDate] = useState("");
  const [newTaskTimeSpecific, setNewTaskTimeSpecific] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("today");

  const folders = ["Work", "Personal", "Hobby", "Other"];

  const addTask = () => {
    if (!newTask.trim()) return;

    const task: Task = {
      id: Date.now().toString(),
      title: newTask,
      completed: false,
      folder: newTaskFolder,
      dueDate: newTaskDueDate ? new Date(newTaskDueDate) : undefined,
      isTimeSpecific: newTaskTimeSpecific,
      createdAt: new Date(),
    };

    setTasks(prev => [task, ...prev]);
    setNewTask("");
    setNewTaskDueDate("");
  };

  const toggleTask = (id: string) => {
    setTasks(prev => prev.map(task => 
      task.id === id ? { ...task, completed: !task.completed } : task
    ));
  };

  const deleteTask = (id: string) => {
    setTasks(prev => prev.filter(task => task.id !== id));
  };

  const getTodaysTasks = () => tasks.filter(task =>
    !task.completed && (
      (task.dueDate && isToday(task.dueDate)) ||
      (!task.dueDate && !task.isTimeSpecific)
    )
  );

  const getTomorrowsTasks = () => tasks.filter(task =>
    !task.completed && task.dueDate && isTomorrow(task.dueDate)
  );

  const getTasksByFolder = (folder: string) => tasks.filter(task =>
    task.folder === folder && task.title.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const filteredTasks = tasks.filter(task =>
    task.title.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const completedCount = tasks.filter(task => task.completed).length;
  const totalCount = tasks.length;

  // TaskItem component
  const TaskItem = ({ task, onToggle, onDelete }: { task: Task; onToggle: (id: string) => void; onDelete: (id: string) => void }) => (
    <div className="group flex items-center gap-4 p-3 hover:bg-white/5 rounded-lg transition-colors">
      <Checkbox
        checked={task.completed}
        onCheckedChange={() => onToggle(task.id)}
        className="flex-shrink-0"
      />

      <div className="flex-1 min-w-0">
        <p className={`text-sm font-medium transition-colors ${
          task.completed ? "line-through text-muted-foreground" : "text-foreground"
        }`}>
          {task.title}
        </p>
        <div className="flex items-center gap-2 mt-1">
          <span className="text-xs text-muted-foreground">{task.folder}</span>
          {task.dueDate && (
            <>
              <span className="text-xs text-muted-foreground">•</span>
              <span className={`text-xs ${
                isToday(task.dueDate) ? "text-primary" :
                isTomorrow(task.dueDate) ? "text-blue-500" : "text-muted-foreground"
              }`}>
                {isToday(task.dueDate) ? "Today" :
                 isTomorrow(task.dueDate) ? "Tomorrow" :
                 format(task.dueDate, "MMM d")}
              </span>
            </>
          )}
          {!task.isTimeSpecific && (
            <>
              <span className="text-xs text-muted-foreground">•</span>
              <span className="text-xs text-muted-foreground">Anytime</span>
            </>
          )}
        </div>
      </div>

      <div className="flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
          <Edit className="w-3 h-3" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onDelete(task.id)}
          className="h-8 w-8 p-0 text-destructive hover:text-destructive"
        >
          <Trash2 className="w-3 h-3" />
        </Button>
      </div>
    </div>
  );

  return (
    <AuthGuard>
      <Layout>
        <div className="space-y-6 vanta-fade-in max-w-6xl mx-auto">
          {/* Clean Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-semibold text-foreground">Tasks</h1>
              <p className="text-muted-foreground mt-1">
                {completedCount} of {totalCount} completed
              </p>
            </div>
            <div className="text-right">
              <div className="text-sm text-muted-foreground">
                {totalCount - completedCount} remaining
              </div>
            </div>
          </div>

          {/* Add Task */}
          <Card className="vanta-card">
            <CardHeader>
              <CardTitle className="text-lg font-semibold">Add New Task</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  placeholder="What needs to be done?"
                  value={newTask}
                  onChange={(e) => setNewTask(e.target.value)}
                  onKeyPress={(e) => e.key === "Enter" && addTask()}
                  className="vanta-input"
                />

                <Select value={newTaskFolder} onValueChange={setNewTaskFolder}>
                  <SelectTrigger className="vanta-input">
                    <SelectValue placeholder="Select folder" />
                  </SelectTrigger>
                  <SelectContent>
                    {folders.map(folder => (
                      <SelectItem key={folder} value={folder}>
                        <div className="flex items-center gap-2">
                          <Folder className="w-4 h-4" />
                          {folder}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-muted-foreground">
                    Due Date (optional)
                  </label>
                  <Input
                    type="date"
                    value={newTaskDueDate}
                    onChange={(e) => setNewTaskDueDate(e.target.value)}
                    className="vanta-input"
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium text-muted-foreground">
                    Task Type
                  </label>
                  <Select
                    value={newTaskTimeSpecific ? "specific" : "anytime"}
                    onValueChange={(value) => setNewTaskTimeSpecific(value === "specific")}
                  >
                    <SelectTrigger className="vanta-input">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="specific">
                        <div className="flex items-center gap-2">
                          <Calendar className="w-4 h-4" />
                          Time Specific
                        </div>
                      </SelectItem>
                      <SelectItem value="anytime">
                        <div className="flex items-center gap-2">
                          <Clock className="w-4 h-4" />
                          Anytime
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <Button onClick={addTask} className="w-full vanta-button">
                <Plus className="w-4 h-4 mr-2" />
                Add Task
              </Button>
            </CardContent>
          </Card>

          {/* Tasks Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="today">Today</TabsTrigger>
              <TabsTrigger value="upcoming">Upcoming</TabsTrigger>
              <TabsTrigger value="folders">Folders</TabsTrigger>
            </TabsList>

            {/* Today's Tasks */}
            <TabsContent value="today" className="space-y-4">
              <Card className="vanta-card">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Calendar className="w-5 h-5" />
                    Today's Tasks
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {getTodaysTasks().length === 0 ? (
                    <div className="text-center py-8">
                      <CheckSquare className="w-12 h-12 text-muted-foreground mx-auto mb-4 opacity-50" />
                      <p className="text-muted-foreground">No tasks for today. Great job!</p>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {getTodaysTasks().map(task => (
                        <TaskItem key={task.id} task={task} onToggle={toggleTask} onDelete={deleteTask} />
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* Upcoming Tasks */}
            <TabsContent value="upcoming" className="space-y-4">
              <Card className="vanta-card">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Clock className="w-5 h-5" />
                    Tomorrow
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {getTomorrowsTasks().length === 0 ? (
                    <div className="text-center py-8">
                      <p className="text-muted-foreground">No tasks for tomorrow</p>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {getTomorrowsTasks().map(task => (
                        <TaskItem key={task.id} task={task} onToggle={toggleTask} onDelete={deleteTask} />
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* Folders */}
            <TabsContent value="folders" className="space-y-4">
              <div className="relative mb-4">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search tasks..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 vanta-input"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {folders.map(folder => {
                  const folderTasks = getTasksByFolder(folder);
                  return (
                    <Card key={folder} className="vanta-card">
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <Folder className="w-5 h-5" />
                          {folder}
                          <span className="text-sm font-normal text-muted-foreground">
                            ({folderTasks.length})
                          </span>
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        {folderTasks.length === 0 ? (
                          <p className="text-muted-foreground text-center py-4">No tasks in this folder</p>
                        ) : (
                          <div className="space-y-3">
                            {folderTasks.map(task => (
                              <TaskItem key={task.id} task={task} onToggle={toggleTask} onDelete={deleteTask} />
                            ))}
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </TabsContent>
          </Tabs>


        </div>
      </Layout>
    </AuthGuard>
  );
}
