import { useState, useEffect } from "react";
import { <PERSON>uote, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react";
import { Button } from "@/components/ui/button";

interface QuoteData {
  text: string;
  author: string;
  category: string;
}

const motivationalQuotes: QuoteData[] = [
  {
    text: "The only way to do great work is to love what you do.",
    author: "Steve Jobs",
    category: "Success"
  },
  {
    text: "Innovation distinguishes between a leader and a follower.",
    author: "<PERSON> Jobs",
    category: "Innovation"
  },
  {
    text: "Your time is limited, don't waste it living someone else's life.",
    author: "<PERSON>s",
    category: "Life"
  },
  {
    text: "The future belongs to those who believe in the beauty of their dreams.",
    author: "<PERSON>",
    category: "Dreams"
  },
  {
    text: "It is during our darkest moments that we must focus to see the light.",
    author: "Aristotle",
    category: "Perseverance"
  },
  {
    text: "Success is not final, failure is not fatal: it is the courage to continue that counts.",
    author: "<PERSON>",
    category: "Courage"
  },
  {
    text: "The only impossible journey is the one you never begin.",
    author: "<PERSON>",
    category: "Beginning"
  },
  {
    text: "Believe you can and you're halfway there.",
    author: "<PERSON>",
    category: "Belief"
  },
  {
    text: "Don't watch the clock; do what it does. Keep going.",
    author: "Sam Levenson",
    category: "Persistence"
  },
  {
    text: "The best time to plant a tree was 20 years ago. The second best time is now.",
    author: "Chinese Proverb",
    category: "Action"
  }
];

export default function MotivationalQuote() {
  const [currentQuote, setCurrentQuote] = useState<QuoteData>(motivationalQuotes[0]);
  const [isRefreshing, setIsRefreshing] = useState(false);

  useEffect(() => {
    // Set a random quote on component mount
    const randomIndex = Math.floor(Math.random() * motivationalQuotes.length);
    setCurrentQuote(motivationalQuotes[randomIndex]);
  }, []);

  const getNewQuote = () => {
    setIsRefreshing(true);
    setTimeout(() => {
      const randomIndex = Math.floor(Math.random() * motivationalQuotes.length);
      setCurrentQuote(motivationalQuotes[randomIndex]);
      setIsRefreshing(false);
    }, 500);
  };

  return (
    <div className="vanta-card p-6 bg-gradient-to-br from-purple-500/10 to-pink-500/5 border-purple-500/20 relative overflow-hidden">
      <div className="flex items-start gap-4">
        <div className="p-3 rounded-xl bg-purple-500/20">
          <Quote className="h-6 w-6 text-purple-500" />
        </div>
        <div className="flex-1">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              <h3 className="font-semibold text-foreground">Daily Motivation</h3>
              <Sparkles className="h-4 w-4 text-purple-500 animate-pulse" />
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={getNewQuote}
              disabled={isRefreshing}
              className="h-8 w-8 p-0 hover:bg-purple-500/20"
            >
              <RefreshCw className={`h-4 w-4 text-purple-500 ${isRefreshing ? 'animate-spin' : ''}`} />
            </Button>
          </div>
          
          <blockquote className="text-foreground/90 italic leading-relaxed mb-3">
            "{currentQuote.text}"
          </blockquote>
          
          <div className="flex items-center justify-between">
            <cite className="text-sm text-muted-foreground not-italic">
              — {currentQuote.author}
            </cite>
            <span className="text-xs px-2 py-1 bg-purple-500/20 text-purple-500 rounded-full">
              {currentQuote.category}
            </span>
          </div>
        </div>
      </div>
      
      {/* Animated background effect */}
      <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-purple-500/20 to-transparent rounded-full blur-2xl -translate-y-8 translate-x-8" />
    </div>
  );
}
