import { useState } from "react";
import { Plus, Star, ExternalLink, Gift, DollarSign, Heart, Search, Filter, Tag, Calendar, TrendingUp, ShoppingBag, CheckCircle } from "lucide-react";
import Layout from "@/components/Layout";
import AuthGuard from "@/components/AuthGuard";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { format } from "date-fns";

interface WishlistItem {
  id: string;
  name: string;
  description?: string;
  price?: number;
  link?: string;
  category: string;
  priority: "low" | "medium" | "high";
  purchased: boolean;
  dateAdded: Date;
}

export default function Wishlist() {
  const [items] = useState<WishlistItem[]>([
    // Empty by default - ready for production use
  ]);

  const [showAddForm, setShowAddForm] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("All");

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high": return "text-destructive";
      case "medium": return "text-obsidian-warning";
      case "low": return "text-obsidian-success";
      default: return "text-muted-foreground";
    }
  };

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case "high": return "destructive";
      case "medium": return "secondary";
      case "low": return "outline";
      default: return "outline";
    }
  };

  const totalValue = items
    .filter(item => !item.purchased && item.price)
    .reduce((sum, item) => sum + (item.price || 0), 0);

  const categories = ["All", "Electronics", "Accessories", "Kitchen", "Books", "Clothing"];

  const filteredItems = items.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.description?.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === "All" || item.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  return (
    <AuthGuard>
      <Layout>
        <div className="space-y-6 vanta-fade-in">
          {/* Header */}
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
            <div>
              <h1 className="text-3xl font-semibold text-foreground">
                Wishlist
              </h1>
              <p className="text-muted-foreground mt-1">
                Save and track the things you want most
              </p>
            </div>
            <div className="flex items-center gap-3">
              <Button variant="outline" size="sm" className="gap-2">
                <Gift className="h-4 w-4" />
                Purchased
              </Button>
              <Button
                size="sm"
                className="gap-2 vanta-button"
                onClick={() => setShowAddForm(!showAddForm)}
              >
                <Plus className="h-4 w-4" />
                Add Item
              </Button>
            </div>
          </div>

        {/* Wishlist Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardContent className="p-6 text-center">
              <Star className="h-8 w-8 text-obsidian-accent mx-auto mb-2" />
              <p className="text-2xl font-bold text-foreground">{items.length}</p>
              <p className="text-sm text-muted-foreground">Total Items</p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6 text-center">
              <DollarSign className="h-8 w-8 text-primary mx-auto mb-2" />
              <p className="text-2xl font-bold text-foreground">R{totalValue.toFixed(2)}</p>
              <p className="text-sm text-muted-foreground">Total Value</p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6 text-center">
              <Gift className="h-8 w-8 text-obsidian-success mx-auto mb-2" />
              <p className="text-2xl font-bold text-foreground">
                {items.filter(item => item.purchased).length}
              </p>
              <p className="text-sm text-muted-foreground">Purchased</p>
            </CardContent>
          </Card>
        </div>

        {/* Add Item Form */}
        {showAddForm && (
          <Card>
            <CardContent className="p-6">
              <h3 className="font-semibold text-foreground mb-4">Add New Item</h3>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input placeholder="Item name" />
                  <Input placeholder="Price (optional)" type="number" />
                  <Input placeholder="Link (optional)" />
                  <select className="px-3 py-2 bg-background border border-border rounded-lg text-sm">
                    <option value="">Select category</option>
                    {categories.slice(1).map(category => (
                      <option key={category} value={category}>{category}</option>
                    ))}
                  </select>
                </div>
                <Textarea placeholder="Description (optional)" />
                <div className="flex gap-3">
                  <Button className="flex-1">Add to Wishlist</Button>
                  <Button 
                    variant="outline" 
                    onClick={() => setShowAddForm(false)}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Search and Filter */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search wishlist items..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 vanta-input"
            />
          </div>
          <div className="flex flex-wrap gap-2">
            {categories.map(category => (
              <Button
                key={category}
                variant={selectedCategory === category ? "default" : "outline"}
                size="sm"
                className="rounded-full"
                onClick={() => setSelectedCategory(category)}
              >
                {category}
              </Button>
            ))}
          </div>
        </div>

        {/* Wishlist Items */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredItems.length === 0 ? (
            <div className="col-span-full text-center py-12">
              <Star className="w-12 h-12 text-muted-foreground mx-auto mb-4 opacity-50" />
              <p className="text-muted-foreground">
                {searchQuery || selectedCategory !== "All"
                  ? "No items match your search criteria"
                  : "Your wishlist is empty. Add some items to get started!"}
              </p>
            </div>
          ) : (
            filteredItems.map(item => (
            <Card 
              key={item.id}
              className={`hover:shadow-lg transition-shadow ${
                item.purchased ? "opacity-60" : ""
              }`}
            >
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-3">
                  <h3 className={`font-semibold text-foreground ${
                    item.purchased ? "line-through" : ""
                  }`}>
                    {item.name}
                  </h3>
                  <Star className={`h-5 w-5 ${
                    item.purchased ? "text-obsidian-success" : "text-muted-foreground"
                  }`} />
                </div>
                
                {item.description && (
                  <p className="text-sm text-muted-foreground mb-3">
                    {item.description}
                  </p>
                )}

                <div className="flex items-center gap-2 mb-3">
                  <Badge variant={getPriorityBadge(item.priority)}>
                    {item.priority} priority
                  </Badge>
                  <Badge variant="outline">{item.category}</Badge>
                </div>

                {item.price && (
                  <p className="text-lg font-semibold text-primary mb-3">
                    R{item.price.toFixed(2)}
                  </p>
                )}

                <div className="flex gap-2">
                  {!item.purchased && (
                    <Button size="sm" className="flex-1">
                      <Gift className="h-4 w-4 mr-2" />
                      Mark Purchased
                    </Button>
                  )}
                  {item.link && (
                    <Button variant="outline" size="sm">
                      <ExternalLink className="h-4 w-4" />
                    </Button>
                  )}
                </div>

                <p className="text-xs text-muted-foreground mt-3">
                  Added {item.dateAdded.toLocaleDateString()}
                </p>
              </CardContent>
            </Card>
            ))
          )}
        </div>
      </div>
    </Layout>
  </AuthGuard>
  );
}