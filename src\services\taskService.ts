import { supabase } from '@/integrations/supabase/client';
import type { Tables, TablesInsert, TablesUpdate } from '@/integrations/supabase/types';

export type Task = Tables<'tasks'>;
export type TaskInsert = TablesInsert<'tasks'>;
export type TaskUpdate = TablesUpdate<'tasks'>;

export type Subtask = Tables<'subtasks'>;
export type SubtaskInsert = TablesInsert<'subtasks'>;

class TaskService {
  async getTasks(userId: string): Promise<Task[]> {
    try {
      const { data, error } = await supabase
        .from('tasks')
        .select('*')
        .eq('user_id', userId)
        .eq('archived', false)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching tasks:', error);
      throw new Error('Failed to fetch tasks');
    }
  }

  async getTasksByFolder(userId: string, folder: string): Promise<Task[]> {
    try {
      const { data, error } = await supabase
        .from('tasks')
        .select('*')
        .eq('user_id', userId)
        .eq('folder', folder)
        .eq('archived', false)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching tasks by folder:', error);
      throw new Error('Failed to fetch tasks');
    }
  }

  async getTodaysTasks(userId: string): Promise<Task[]> {
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      const { data, error } = await supabase
        .from('tasks')
        .select('*')
        .eq('user_id', userId)
        .eq('archived', false)
        .gte('due_date', today.toISOString())
        .lt('due_date', tomorrow.toISOString())
        .order('due_date', { ascending: true });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching today\'s tasks:', error);
      throw new Error('Failed to fetch today\'s tasks');
    }
  }

  async createTask(task: TaskInsert): Promise<Task> {
    try {
      const { data, error } = await supabase
        .from('tasks')
        .insert(task)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating task:', error);
      throw new Error('Failed to create task');
    }
  }

  async updateTask(id: string, updates: TaskUpdate): Promise<Task> {
    try {
      const { data, error } = await supabase
        .from('tasks')
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error updating task:', error);
      throw new Error('Failed to update task');
    }
  }

  async deleteTask(id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('tasks')
        .delete()
        .eq('id', id);

      if (error) throw error;
    } catch (error) {
      console.error('Error deleting task:', error);
      throw new Error('Failed to delete task');
    }
  }

  async toggleTaskComplete(id: string): Promise<Task> {
    try {
      // First get the current task
      const { data: currentTask, error: fetchError } = await supabase
        .from('tasks')
        .select('status')
        .eq('id', id)
        .single();

      if (fetchError) throw fetchError;

      const newStatus = currentTask.status === 'completed' ? 'pending' : 'completed';
      const completedAt = newStatus === 'completed' ? new Date().toISOString() : null;

      const { data, error } = await supabase
        .from('tasks')
        .update({ 
          status: newStatus,
          completed_at: completedAt,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error toggling task completion:', error);
      throw new Error('Failed to toggle task completion');
    }
  }

  async getSubtasks(taskId: string): Promise<Subtask[]> {
    try {
      const { data, error } = await supabase
        .from('subtasks')
        .select('*')
        .eq('task_id', taskId)
        .order('created_at', { ascending: true });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching subtasks:', error);
      throw new Error('Failed to fetch subtasks');
    }
  }

  async createSubtask(subtask: SubtaskInsert): Promise<Subtask> {
    try {
      const { data, error } = await supabase
        .from('subtasks')
        .insert(subtask)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating subtask:', error);
      throw new Error('Failed to create subtask');
    }
  }

  async toggleSubtaskComplete(id: string): Promise<Subtask> {
    try {
      // First get the current subtask
      const { data: currentSubtask, error: fetchError } = await supabase
        .from('subtasks')
        .select('completed')
        .eq('id', id)
        .single();

      if (fetchError) throw fetchError;

      const { data, error } = await supabase
        .from('subtasks')
        .update({ completed: !currentSubtask.completed })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error toggling subtask completion:', error);
      throw new Error('Failed to toggle subtask completion');
    }
  }

  async deleteSubtask(id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('subtasks')
        .delete()
        .eq('id', id);

      if (error) throw error;
    } catch (error) {
      console.error('Error deleting subtask:', error);
      throw new Error('Failed to delete subtask');
    }
  }

  // Get task statistics
  async getTaskStats(userId: string): Promise<{
    total: number;
    completed: number;
    pending: number;
    overdue: number;
  }> {
    try {
      const { data: allTasks, error } = await supabase
        .from('tasks')
        .select('status, due_date')
        .eq('user_id', userId)
        .eq('archived', false);

      if (error) throw error;

      const now = new Date();
      const stats = {
        total: allTasks.length,
        completed: allTasks.filter(t => t.status === 'completed').length,
        pending: allTasks.filter(t => t.status === 'pending').length,
        overdue: allTasks.filter(t => 
          t.status !== 'completed' && 
          t.due_date && 
          new Date(t.due_date) < now
        ).length
      };

      return stats;
    } catch (error) {
      console.error('Error fetching task stats:', error);
      throw new Error('Failed to fetch task statistics');
    }
  }
}

export const taskService = new TaskService();
