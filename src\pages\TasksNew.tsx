import { useState } from "react";
import { Plus, Search, CheckSquare, Square, Trash2, Edit } from "lucide-react";
import Layout from "@/components/Layout";
import AuthGuard from "@/components/AuthGuard";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";

interface Task {
  id: string;
  title: string;
  completed: boolean;
  createdAt: Date;
}

export default function Tasks() {
  const [tasks, setTasks] = useState<Task[]>([
    { id: "1", title: "Review project proposal", completed: false, createdAt: new Date() },
    { id: "2", title: "Call dentist for appointment", completed: true, createdAt: new Date() },
    { id: "3", title: "Grocery shopping", completed: false, createdAt: new Date() },
    { id: "4", title: "Prepare presentation slides", completed: false, createdAt: new Date() },
    { id: "5", title: "Book flight tickets", completed: true, createdAt: new Date() },
  ]);
  
  const [newTask, setNewTask] = useState("");
  const [searchQuery, setSearchQuery] = useState("");

  const addTask = () => {
    if (!newTask.trim()) return;
    
    const task: Task = {
      id: Date.now().toString(),
      title: newTask,
      completed: false,
      createdAt: new Date(),
    };
    
    setTasks(prev => [task, ...prev]);
    setNewTask("");
  };

  const toggleTask = (id: string) => {
    setTasks(prev => prev.map(task => 
      task.id === id ? { ...task, completed: !task.completed } : task
    ));
  };

  const deleteTask = (id: string) => {
    setTasks(prev => prev.filter(task => task.id !== id));
  };

  const filteredTasks = tasks.filter(task =>
    task.title.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const completedCount = tasks.filter(task => task.completed).length;
  const totalCount = tasks.length;

  return (
    <AuthGuard>
      <Layout>
        <div className="space-y-6 vanta-fade-in max-w-4xl mx-auto">
          {/* Clean Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-semibold text-foreground vanta-title">Tasks</h1>
              <p className="text-muted-foreground vanta-subtitle mt-1">
                {completedCount} of {totalCount} completed
              </p>
            </div>
            <div className="text-right">
              <div className="text-sm text-muted-foreground">
                {totalCount - completedCount} remaining
              </div>
            </div>
          </div>

          {/* Add Task */}
          <Card className="vanta-card">
            <CardContent className="p-6">
              <div className="flex gap-3">
                <Input
                  placeholder="Add a new task..."
                  value={newTask}
                  onChange={(e) => setNewTask(e.target.value)}
                  onKeyPress={(e) => e.key === "Enter" && addTask()}
                  className="flex-1 vanta-input"
                />
                <Button onClick={addTask} className="vanta-button">
                  <Plus className="w-4 h-4" />
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search tasks..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 vanta-input"
            />
          </div>

          {/* Tasks List */}
          <Card className="vanta-card">
            <CardContent className="p-0">
              {filteredTasks.length === 0 ? (
                <div className="p-12 text-center">
                  <CheckSquare className="w-12 h-12 text-muted-foreground mx-auto mb-4 opacity-50" />
                  <p className="text-muted-foreground">
                    {searchQuery ? "No tasks match your search" : "No tasks yet. Add one above to get started."}
                  </p>
                </div>
              ) : (
                <div className="divide-y divide-border/30">
                  {filteredTasks.map((task, index) => (
                    <div
                      key={task.id}
                      className="group flex items-center gap-4 p-4 hover:bg-white/5 transition-colors"
                    >
                      <Checkbox
                        checked={task.completed}
                        onCheckedChange={() => toggleTask(task.id)}
                        className="flex-shrink-0"
                      />
                      
                      <div className="flex-1 min-w-0">
                        <p
                          className={`text-sm font-medium transition-colors ${
                            task.completed
                              ? "line-through text-muted-foreground"
                              : "text-foreground"
                          }`}
                        >
                          {task.title}
                        </p>
                      </div>
                      
                      <div className="flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0"
                        >
                          <Edit className="w-3 h-3" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => deleteTask(task.id)}
                          className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                        >
                          <Trash2 className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Progress Summary */}
          {totalCount > 0 && (
            <Card className="vanta-card">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium text-foreground">Progress</h3>
                    <p className="text-sm text-muted-foreground">
                      Keep up the great work!
                    </p>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-semibold text-foreground">
                      {Math.round((completedCount / totalCount) * 100)}%
                    </div>
                    <div className="text-xs text-muted-foreground">
                      Complete
                    </div>
                  </div>
                </div>
                
                <div className="mt-4 w-full bg-muted rounded-full h-2">
                  <div 
                    className="bg-primary h-2 rounded-full transition-all duration-300"
                    style={{ width: `${(completedCount / totalCount) * 100}%` }}
                  />
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </Layout>
    </AuthGuard>
  );
}
