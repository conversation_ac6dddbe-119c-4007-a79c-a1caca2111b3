import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import {
  Plus,
  DollarSign,
  TrendingUp,
  TrendingDown,
  Calendar,
  Clock,
  AlertTriangle,
  CheckCircle,
  ExternalLink,
  Bell,
  CreditCard,
  Smartphone,
  Mail,
  User,
  Edit,
  Trash2,
  Filter
} from "lucide-react";
import Layout from "@/components/Layout";
import AuthGuard from "@/components/AuthGuard";
import { Button } from "@/components/ui/button";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { format, isAfter, isBefore, addDays } from "date-fns";
import { formatCurrency, currencyService } from "@/utils/currency";
import { financeService, type FinanceItem } from "@/services/financeService";
import { useToast } from "@/hooks/use-toast";

export default function Finance() {
  const { toast } = useToast();
  const [user, setUser] = useState<any>(null);
  const [items, setItems] = useState<FinanceItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [filterType, setFilterType] = useState<"all" | "owed" | "owing">("all");
  const [filterPriority, setFilterPriority] = useState<"all" | "low" | "medium" | "high">("all");
  const [showAddForm, setShowAddForm] = useState(false);

  useEffect(() => {
    // Get current user
    const getCurrentUser = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      setUser(user);
    };

    getCurrentUser();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      setUser(session?.user ?? null);
    });

    return () => subscription.unsubscribe();
  }, []);

  useEffect(() => {
    if (user?.id) {
      loadFinanceItems();
    }
  }, [user?.id]);

  const loadFinanceItems = async () => {
    try {
      setLoading(true);
      const data = await financeService.getFinanceItems(user!.id);
      setItems(data);
    } catch (error: any) {
      console.error('Error loading finance items:', error);
      toast({
        title: "Error",
        description: "Failed to load finance items. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleTogglePaid = async (id: string) => {
    try {
      await financeService.togglePaidStatus(id);
      await loadFinanceItems(); // Reload data
      toast({
        title: "Success",
        description: "Payment status updated successfully.",
      });
    } catch (error: any) {
      console.error('Error toggling paid status:', error);
      toast({
        title: "Error",
        description: "Failed to update payment status. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Filter items based on selected filters
  const filteredItems = items.filter(item => {
    const typeMatch = filterType === "all" || item.type === filterType;
    const priorityMatch = filterPriority === "all" || item.priority === filterPriority;
    return typeMatch && priorityMatch;
  });

  const totalOwed = items
    .filter(item => item.type === "owed" && !item.paid)
    .reduce((sum, item) => sum + item.amount, 0);

  const totalOwing = items
    .filter(item => item.type === "owing" && !item.paid)
    .reduce((sum, item) => sum + item.amount, 0);

  const balance = totalOwed - totalOwing;

  // Calculate overdue items
  const overdueItems = items.filter(item =>
    !item.paid && item.due_date && isBefore(new Date(item.due_date), new Date())
  );

  // Calculate items due soon (within 3 days)
  const dueSoonItems = items.filter(item =>
    !item.paid &&
    item.due_date &&
    isAfter(new Date(item.due_date), new Date()) &&
    isBefore(new Date(item.due_date), addDays(new Date(), 3))
  );

  const getCategoryColor = (category: string) => {
    const colors = {
      personal: "bg-blue-500/20 text-blue-500 border-blue-500/30",
      business: "bg-purple-500/20 text-purple-500 border-purple-500/30",
      utilities: "bg-yellow-500/20 text-yellow-500 border-yellow-500/30",
      rent: "bg-red-500/20 text-red-500 border-red-500/30",
      food: "bg-green-500/20 text-green-500 border-green-500/30",
      entertainment: "bg-pink-500/20 text-pink-500 border-pink-500/30",
      other: "bg-gray-500/20 text-gray-500 border-gray-500/30"
    };
    return colors[category as keyof typeof colors] || colors.other;
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high": return "bg-red-500/20 text-red-500 border-red-500/30";
      case "medium": return "bg-yellow-500/20 text-yellow-500 border-yellow-500/30";
      case "low": return "bg-green-500/20 text-green-500 border-green-500/30";
      default: return "bg-gray-500/20 text-gray-500 border-gray-500/30";
    }
  };

  const getPaymentMethodIcon = (method?: string) => {
    switch (method) {
      case "paypal": return <CreditCard className="h-4 w-4" />;
      case "venmo": return <Smartphone className="h-4 w-4" />;
      case "zelle": return <Smartphone className="h-4 w-4" />;
      case "bank_transfer": return <DollarSign className="h-4 w-4" />;
      default: return <DollarSign className="h-4 w-4" />;
    }
  };

  return (
    <AuthGuard>
      <Layout>
        <div className="space-y-6 vanta-fade-in">
          {/* Header */}
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
            <div>
              <h1 className="text-3xl font-semibold text-foreground">
                Finance Tracker
              </h1>
              <p className="text-muted-foreground mt-1">
                Manage your money with smart tracking and reminders
              </p>
            </div>
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                className="gap-2"
                onClick={() => setShowAddForm(!showAddForm)}
              >
                <Filter className="h-4 w-4" />
                Filters
              </Button>
              <Button size="sm" className="gap-2 vanta-button">
                <Plus className="h-4 w-4" />
                Add Transaction
              </Button>
            </div>
          </div>

          {loading && (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          )}

          {/* Alerts for overdue and due soon */}
          {(overdueItems.length > 0 || dueSoonItems.length > 0) && (
            <div className="space-y-3">
              {overdueItems.length > 0 && (
                <div className="vanta-card p-4 bg-gradient-to-r from-red-500/10 to-red-500/5 border-red-500/30">
                  <div className="flex items-center gap-3">
                    <AlertTriangle className="h-5 w-5 text-red-500" />
                    <div>
                      <p className="font-semibold text-red-500">
                        {overdueItems.length} overdue item{overdueItems.length > 1 ? 's' : ''}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        Total overdue: {formatCurrency(overdueItems.reduce((sum, item) => sum + item.amount, 0))}
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {dueSoonItems.length > 0 && (
                <div className="vanta-card p-4 bg-gradient-to-r from-yellow-500/10 to-yellow-500/5 border-yellow-500/30">
                  <div className="flex items-center gap-3">
                    <Clock className="h-5 w-5 text-yellow-500" />
                    <div>
                      <p className="font-semibold text-yellow-500">
                        {dueSoonItems.length} item{dueSoonItems.length > 1 ? 's' : ''} due soon
                      </p>
                      <p className="text-sm text-muted-foreground">
                        Due within 3 days
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Financial Summary */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="vanta-card p-6 bg-gradient-to-br from-green-500/10 to-emerald-500/5 border-green-500/20">
              <div className="flex items-center gap-3">
                <div className="p-3 bg-green-500/20 rounded-xl">
                  <TrendingUp className="h-6 w-6 text-green-500" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Money Owed to You</p>
                  <p className="text-2xl font-bold text-green-500">{formatCurrency(totalOwed)}</p>
                  <p className="text-xs text-muted-foreground">
                    {items.filter(item => item.type === "owed" && !item.paid).length} items
                  </p>
                </div>
              </div>
            </div>

            <div className="vanta-card p-6 bg-gradient-to-br from-red-500/10 to-rose-500/5 border-red-500/20">
              <div className="flex items-center gap-3">
                <div className="p-3 bg-red-500/20 rounded-xl">
                  <TrendingDown className="h-6 w-6 text-red-500" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">You Owe</p>
                  <p className="text-2xl font-bold text-red-500">{formatCurrency(totalOwing)}</p>
                  <p className="text-xs text-muted-foreground">
                    {items.filter(item => item.type === "owing" && !item.paid).length} items
                  </p>
                </div>
              </div>
            </div>

            <div className={`vanta-card p-6 bg-gradient-to-br ${
              balance >= 0
                ? 'from-blue-500/10 to-cyan-500/5 border-blue-500/20'
                : 'from-orange-500/10 to-red-500/5 border-orange-500/20'
            }`}>
              <div className="flex items-center gap-3">
                <div className={`p-3 rounded-xl ${
                  balance >= 0 ? 'bg-blue-500/20' : 'bg-orange-500/20'
                }`}>
                  <DollarSign className={`h-6 w-6 ${
                    balance >= 0 ? 'text-blue-500' : 'text-orange-500'
                  }`} />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Net Balance</p>
                  <p className={`text-2xl font-bold ${
                    balance >= 0 ? 'text-blue-500' : 'text-orange-500'
                  }`}>
                    {balance >= 0 ? '+' : ''}{formatCurrency(Math.abs(balance))}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {balance >= 0 ? 'You\'re ahead!' : 'Pay attention'}
                  </p>
                </div>
              </div>
            </div>

            <div className="vanta-card p-6 bg-gradient-to-br from-purple-500/10 to-pink-500/5 border-purple-500/20">
              <div className="flex items-center gap-3">
                <div className="p-3 bg-purple-500/20 rounded-xl">
                  <Bell className="h-6 w-6 text-purple-500" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Active Reminders</p>
                  <p className="text-2xl font-bold text-purple-500">
                    {items.filter(item => item.reminderSet && !item.paid).length}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Notifications set
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Filters */}
          <div className="vanta-card p-6">
            <h3 className="font-semibold text-foreground mb-4 flex items-center gap-2">
              <Filter className="h-5 w-5 text-primary" />
              Filters & Quick Actions
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground mb-2 block">Type</label>
                <Select value={filterType} onValueChange={(value: any) => setFilterType(value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="All types" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="owed">Money Owed to Me</SelectItem>
                    <SelectItem value="owing">Money I Owe</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium text-muted-foreground mb-2 block">Priority</label>
                <Select value={filterPriority} onValueChange={(value: any) => setFilterPriority(value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="All priorities" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Priorities</SelectItem>
                    <SelectItem value="high">High Priority</SelectItem>
                    <SelectItem value="medium">Medium Priority</SelectItem>
                    <SelectItem value="low">Low Priority</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-end">
                <Button className="w-full vanta-button">
                  <Plus className="h-4 w-4 mr-2" />
                  Add New Transaction
                </Button>
              </div>
            </div>
          </div>

          {/* Tabbed Interface */}
          <Tabs defaultValue="overview" className="space-y-6">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="expenses">Monthly Expenses</TabsTrigger>
              <TabsTrigger value="owed">Money Owed</TabsTrigger>
              <TabsTrigger value="owing">Money Owing</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-xl font-semibold text-foreground">
                  All Transactions ({filteredItems.length})
                </h3>
                <div className="text-sm text-muted-foreground">
                  Showing {filteredItems.length} of {items.length} items
                </div>
              </div>

            <div className="space-y-4">
              {filteredItems.map(item => {
                const isOverdue = !item.paid && item.due_date && isBefore(new Date(item.due_date), new Date());
                const isDueSoon = !item.paid && item.due_date && isAfter(new Date(item.due_date), new Date()) && isBefore(new Date(item.due_date), addDays(new Date(), 3));

                return (
                  <div
                    key={item.id}
                    className={`vanta-card p-6 transition-all duration-200 hover:shadow-lg ${
                      isOverdue ? 'border-red-500/50 bg-red-500/5' :
                      isDueSoon ? 'border-yellow-500/50 bg-yellow-500/5' : ''
                    }`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-3 mb-3">
                          <div className={`w-3 h-3 rounded-full ${
                            item.type === "owed" ? "bg-green-500" : "bg-red-500"
                          }`} />
                          <h4 className="font-semibold text-foreground truncate">{item.description}</h4>
                          <Badge variant="outline" className={getPriorityColor(item.priority)}>
                            {item.priority}
                          </Badge>
                          <Badge variant="outline" className={getCategoryColor(item.category)}>
                            {item.category}
                          </Badge>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-muted-foreground">
                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              <User className="h-4 w-4" />
                              <span>{item.person_name}</span>
                            </div>
                            {item.person_email && (
                              <div className="flex items-center gap-2">
                                <Mail className="h-4 w-4" />
                                <span className="truncate">{item.person_email}</span>
                              </div>
                            )}
                            {item.due_date && (
                              <div className="flex items-center gap-2">
                                <Calendar className="h-4 w-4" />
                                <span>Due {format(new Date(item.due_date), "MMM d, yyyy")}</span>
                                {isOverdue && <Badge variant="destructive" className="text-xs">Overdue</Badge>}
                                {isDueSoon && <Badge variant="outline" className="text-xs border-yellow-500 text-yellow-500">Due Soon</Badge>}
                              </div>
                            )}
                          </div>

                          <div className="space-y-2">
                            {item.payment_method && (
                              <div className="flex items-center gap-2">
                                {getPaymentMethodIcon(item.payment_method)}
                                <span className="capitalize">{item.payment_method.replace('_', ' ')}</span>
                              </div>
                            )}
                            {item.reminder_set && (
                              <div className="flex items-center gap-2">
                                <Bell className="h-4 w-4" />
                                <span>Reminder set</span>
                              </div>
                            )}
                            {item.notes && (
                              <p className="text-xs text-muted-foreground italic truncate">
                                "{item.notes}"
                              </p>
                            )}
                          </div>
                        </div>
                      </div>

                      <div className="text-right ml-4">
                        <p className={`text-2xl font-bold ${
                          item.type === "owed" ? "text-green-500" : "text-red-500"
                        }`}>
                          {formatCurrency(item.amount)}
                        </p>
                        <div className="flex items-center gap-2 mt-2">
                          {item.paymentLink && (
                            <Button size="sm" variant="outline" className="text-xs">
                              <ExternalLink className="h-3 w-3 mr-1" />
                              Pay
                            </Button>
                          )}
                          <Button size="sm" variant="outline" className="text-xs">
                            <Edit className="h-3 w-3 mr-1" />
                            Edit
                          </Button>
                          <Button size="sm" variant="outline" className="text-xs text-red-500 hover:text-red-600">
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                        <Button
                          size="sm"
                          className="mt-2 w-full"
                          variant={item.paid ? "outline" : "default"}
                          onClick={() => handleTogglePaid(item.id)}
                        >
                          <CheckCircle className="h-3 w-3 mr-1" />
                          {item.paid ? "Mark Unpaid" : "Mark Paid"}
                        </Button>
                      </div>
                    </div>
                  </div>
                );
              })}

              {filteredItems.length === 0 && !loading && (
                <div className="text-center py-12">
                  <DollarSign className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">
                    {items.length === 0
                      ? "No transactions yet. Add your first transaction to get started!"
                      : "No transactions found with current filters"
                    }
                  </p>
                  {items.length > 0 && (
                    <Button className="mt-4" onClick={() => {
                      setFilterType("all");
                      setFilterPriority("all");
                    }}>
                      Clear Filters
                    </Button>
                  )}
                </div>
              )}
            </div>
            </TabsContent>

            <TabsContent value="expenses" className="space-y-6">
              <div className="vanta-card p-6">
                <h3 className="text-xl font-semibold text-foreground mb-4">Monthly Expenses</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div className="text-center p-4 bg-red-500/10 rounded-lg">
                    <p className="text-2xl font-bold text-red-500">{formatCurrency(totalOwing)}</p>
                    <p className="text-sm text-muted-foreground">This Month</p>
                  </div>
                  <div className="text-center p-4 bg-blue-500/10 rounded-lg">
                    <p className="text-2xl font-bold text-blue-500">{formatCurrency(totalOwing / 30)}</p>
                    <p className="text-sm text-muted-foreground">Daily Average</p>
                  </div>
                  <div className="text-center p-4 bg-green-500/10 rounded-lg">
                    <p className="text-2xl font-bold text-green-500">{items.filter(i => i.paid).length}</p>
                    <p className="text-sm text-muted-foreground">Paid Items</p>
                  </div>
                </div>

                <div className="mt-6">
                  <h4 className="font-medium text-foreground mb-3">Expense Categories</h4>
                  <div className="space-y-2">
                    {['personal', 'business', 'utilities', 'food', 'entertainment'].map(category => {
                      const categoryTotal = items
                        .filter(item => item.type === 'owing' && item.category === category)
                        .reduce((sum, item) => sum + item.amount, 0);

                      return (
                        <div key={category} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                          <span className="capitalize text-foreground">{category}</span>
                          <span className="font-medium text-foreground">{formatCurrency(categoryTotal)}</span>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="owed" className="space-y-4">
              <h3 className="text-xl font-semibold text-foreground">Money Owed to You</h3>
              <div className="space-y-4">
                {items.filter(item => item.type === 'owed').map(item => (
                  <div key={item.id} className="vanta-card p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium text-foreground">{item.description}</h4>
                        <p className="text-sm text-muted-foreground">{item.person_name}</p>
                      </div>
                      <div className="text-right">
                        <p className="font-bold text-green-500">{formatCurrency(item.amount)}</p>
                        <p className="text-xs text-muted-foreground">
                          {item.due_date ? format(new Date(item.due_date), "MMM d") : 'No due date'}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="owing" className="space-y-4">
              <h3 className="text-xl font-semibold text-foreground">Money You Owe</h3>
              <div className="space-y-4">
                {items.filter(item => item.type === 'owing').map(item => (
                  <div key={item.id} className="vanta-card p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium text-foreground">{item.description}</h4>
                        <p className="text-sm text-muted-foreground">{item.person_name}</p>
                      </div>
                      <div className="text-right">
                        <p className="font-bold text-red-500">{formatCurrency(item.amount)}</p>
                        <p className="text-xs text-muted-foreground">
                          {item.due_date ? format(new Date(item.due_date), "MMM d") : 'No due date'}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </Layout>
    </AuthGuard>
  );
}