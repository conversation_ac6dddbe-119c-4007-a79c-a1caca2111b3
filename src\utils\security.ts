// Security utilities and configurations for Vanta App

import { supabase } from '@/integrations/supabase/client';

// Input sanitization
export function sanitizeInput(input: string): string {
  if (!input) return '';
  
  return input
    .trim()
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove script tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+\s*=/gi, '') // Remove event handlers
    .replace(/[<>]/g, ''); // Remove angle brackets
}

// Validate email format
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Validate password strength
export function validatePassword(password: string): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];
  
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }
  
  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }
  
  if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    errors.push('Password must contain at least one special character');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

// Rate limiting for API calls
class RateLimiter {
  private requests: Map<string, number[]> = new Map();
  private readonly maxRequests: number;
  private readonly windowMs: number;

  constructor(maxRequests: number = 100, windowMs: number = 60000) {
    this.maxRequests = maxRequests;
    this.windowMs = windowMs;
  }

  isAllowed(identifier: string): boolean {
    const now = Date.now();
    const requests = this.requests.get(identifier) || [];
    
    // Remove old requests outside the window
    const validRequests = requests.filter(time => now - time < this.windowMs);
    
    if (validRequests.length >= this.maxRequests) {
      return false;
    }
    
    validRequests.push(now);
    this.requests.set(identifier, validRequests);
    
    return true;
  }

  reset(identifier: string): void {
    this.requests.delete(identifier);
  }
}

export const rateLimiter = new RateLimiter();

// Secure session management
export class SecureSession {
  private static readonly SESSION_KEY = 'vanta_session';
  private static readonly BIOMETRIC_KEY = 'vanta_biometric_enabled';

  static async initializeSession(): Promise<void> {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      
      if (session) {
        // Validate session integrity
        const isValid = await this.validateSession(session);
        if (!isValid) {
          await this.clearSession();
          throw new Error('Invalid session detected');
        }
        
        // Update last activity
        this.updateLastActivity();
      }
    } catch (error) {
      console.error('Session initialization failed:', error);
      await this.clearSession();
    }
  }

  static async validateSession(session: any): Promise<boolean> {
    try {
      // Check if session is expired
      if (session.expires_at && new Date(session.expires_at * 1000) < new Date()) {
        return false;
      }
      
      // Verify with Supabase
      const { data: { user } } = await supabase.auth.getUser();
      return !!user;
    } catch {
      return false;
    }
  }

  static updateLastActivity(): void {
    localStorage.setItem('vanta_last_activity', Date.now().toString());
  }

  static isSessionExpired(): boolean {
    const lastActivity = localStorage.getItem('vanta_last_activity');
    if (!lastActivity) return true;
    
    const inactivityLimit = 30 * 60 * 1000; // 30 minutes
    return Date.now() - parseInt(lastActivity) > inactivityLimit;
  }

  static async clearSession(): Promise<void> {
    await supabase.auth.signOut();
    localStorage.removeItem('vanta_last_activity');
    localStorage.removeItem(this.BIOMETRIC_KEY);
    sessionStorage.clear();
  }

  static isBiometricEnabled(): boolean {
    return localStorage.getItem(this.BIOMETRIC_KEY) === 'true';
  }

  static setBiometricEnabled(enabled: boolean): void {
    localStorage.setItem(this.BIOMETRIC_KEY, enabled.toString());
  }
}

// Biometric authentication support
export class BiometricAuth {
  static async isSupported(): Promise<boolean> {
    if (!window.PublicKeyCredential) return false;
    
    try {
      const available = await PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable();
      return available;
    } catch {
      return false;
    }
  }

  static async register(userId: string): Promise<boolean> {
    try {
      if (!await this.isSupported()) {
        throw new Error('Biometric authentication not supported');
      }

      const credential = await navigator.credentials.create({
        publicKey: {
          challenge: new Uint8Array(32),
          rp: {
            name: 'Vanta',
            id: window.location.hostname,
          },
          user: {
            id: new TextEncoder().encode(userId),
            name: userId,
            displayName: 'Vanta User',
          },
          pubKeyCredParams: [{ alg: -7, type: 'public-key' }],
          authenticatorSelection: {
            authenticatorAttachment: 'platform',
            userVerification: 'required',
          },
          timeout: 60000,
          attestation: 'direct',
        },
      });

      if (credential) {
        // Store credential ID securely
        localStorage.setItem('vanta_biometric_credential', credential.id);
        SecureSession.setBiometricEnabled(true);
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Biometric registration failed:', error);
      return false;
    }
  }

  static async authenticate(): Promise<boolean> {
    try {
      if (!await this.isSupported()) {
        throw new Error('Biometric authentication not supported');
      }

      const credentialId = localStorage.getItem('vanta_biometric_credential');
      if (!credentialId) {
        throw new Error('No biometric credential found');
      }

      const credential = await navigator.credentials.get({
        publicKey: {
          challenge: new Uint8Array(32),
          allowCredentials: [{
            id: new TextEncoder().encode(credentialId),
            type: 'public-key',
          }],
          userVerification: 'required',
          timeout: 60000,
        },
      });

      return !!credential;
    } catch (error) {
      console.error('Biometric authentication failed:', error);
      return false;
    }
  }
}

// Data encryption utilities
export class DataEncryption {
  private static readonly ALGORITHM = 'AES-GCM';
  private static readonly KEY_LENGTH = 256;

  static async generateKey(): Promise<CryptoKey> {
    return await crypto.subtle.generateKey(
      {
        name: this.ALGORITHM,
        length: this.KEY_LENGTH,
      },
      true,
      ['encrypt', 'decrypt']
    );
  }

  static async encrypt(data: string, key: CryptoKey): Promise<string> {
    const iv = crypto.getRandomValues(new Uint8Array(12));
    const encodedData = new TextEncoder().encode(data);
    
    const encryptedData = await crypto.subtle.encrypt(
      {
        name: this.ALGORITHM,
        iv: iv,
      },
      key,
      encodedData
    );

    const combined = new Uint8Array(iv.length + encryptedData.byteLength);
    combined.set(iv);
    combined.set(new Uint8Array(encryptedData), iv.length);
    
    return btoa(String.fromCharCode(...combined));
  }

  static async decrypt(encryptedData: string, key: CryptoKey): Promise<string> {
    const combined = new Uint8Array(
      atob(encryptedData).split('').map(char => char.charCodeAt(0))
    );
    
    const iv = combined.slice(0, 12);
    const data = combined.slice(12);
    
    const decryptedData = await crypto.subtle.decrypt(
      {
        name: this.ALGORITHM,
        iv: iv,
      },
      key,
      data
    );

    return new TextDecoder().decode(decryptedData);
  }
}

// Security headers and CSP
export function setupSecurityHeaders(): void {
  // Content Security Policy
  const csp = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
    "font-src 'self' https://fonts.gstatic.com",
    "img-src 'self' data: https:",
    "connect-src 'self' https://*.supabase.co wss://*.supabase.co https://api.openweathermap.org https://api.bigdatacloud.net https://ipapi.co",
    "frame-ancestors 'none'",
    "base-uri 'self'",
    "form-action 'self'"
  ].join('; ');

  const meta = document.createElement('meta');
  meta.httpEquiv = 'Content-Security-Policy';
  meta.content = csp;
  document.head.appendChild(meta);
}

// Initialize security features
export function initializeSecurity(): void {
  setupSecurityHeaders();
  SecureSession.initializeSession();
  
  // Set up session timeout monitoring
  setInterval(() => {
    if (SecureSession.isSessionExpired()) {
      SecureSession.clearSession();
      window.location.href = '/auth';
    }
  }, 60000); // Check every minute

  // Update activity on user interaction
  ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'].forEach(event => {
    document.addEventListener(event, () => {
      SecureSession.updateLastActivity();
    }, { passive: true });
  });

  console.log('Vanta security features initialized');
}
