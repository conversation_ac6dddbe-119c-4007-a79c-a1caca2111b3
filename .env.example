# Vanta App Environment Configuration
# Copy this file to .env.local and fill in your actual values

# Supabase Configuration
VITE_SUPABASE_URL=https://qtlzonnhzyvoumbyyfci.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF0bHpvbm5oenl2b3VtYnl5ZmNpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQ0ODczNjgsImV4cCI6MjA3MDA2MzM2OH0.NAQkB0E7It0WlrSy6o0ONadjbHj5mXGA_19hHFCcNHc

# App Configuration
VITE_APP_NAME=VANTA
VITE_APP_VERSION=1.0.0
VITE_APP_ENVIRONMENT=production

# Security Configuration
VITE_ENABLE_BIOMETRIC_AUTH=true
VITE_SESSION_TIMEOUT_MINUTES=30
VITE_MAX_LOGIN_ATTEMPTS=5

# Feature Flags
VITE_ENABLE_PWA=true
VITE_ENABLE_OFFLINE_MODE=true
VITE_ENABLE_PUSH_NOTIFICATIONS=true
VITE_ENABLE_ANALYTICS=false

# API Configuration
VITE_API_BASE_URL=https://api.vanta.app
VITE_API_TIMEOUT=10000

# Third-party Integrations
VITE_GEMINI_API_KEY=AIzaSyBMWX_4HIW6nxKG98YTjYCQaiEgrm3NwdE
VITE_OPENWEATHER_API_KEY=********************************
VITE_GOOGLE_ANALYTICS_ID=
VITE_SENTRY_DSN=
VITE_VAPID_PUBLIC_KEY=

# User Preferences
VITE_DEFAULT_CURRENCY=ZAR
VITE_DEFAULT_LOCATION=Johannesburg, South Africa
VITE_DEFAULT_TIMEZONE=Africa/Johannesburg

# Security
VITE_ENABLE_NOTES_PIN=true
VITE_DEFAULT_PIN=1234

# Features
VITE_ENABLE_CHAT=false
VITE_ENABLE_SHOPPING=false

# Development Configuration
VITE_DEBUG_MODE=false
VITE_LOG_LEVEL=error

# Database Configuration (for migrations)
DATABASE_URL=postgresql://username:password@localhost:5432/vanta_db

# Deployment Configuration
VITE_BASE_URL=/
VITE_BUILD_PATH=dist

# Security Headers
VITE_ENABLE_CSP=true
VITE_ENABLE_HSTS=true
VITE_ENABLE_FRAME_OPTIONS=true

# Performance Configuration
VITE_ENABLE_SERVICE_WORKER=true
VITE_CACHE_DURATION=86400
VITE_PRELOAD_ROUTES=true

# Monitoring and Logging
VITE_ENABLE_ERROR_REPORTING=true
VITE_ENABLE_PERFORMANCE_MONITORING=true
VITE_LOG_RETENTION_DAYS=30
