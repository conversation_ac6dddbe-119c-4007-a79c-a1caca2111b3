import { useState, useEffect } from "react";
import { Cloud, Sun, CloudRain, CloudSnow, Wind, Thermometer, CloudDrizzle, Zap, Eye } from "lucide-react";
import { weatherService, type WeatherData } from "@/services/weatherService";
import { useToast } from "@/hooks/use-toast";

export default function WeatherWidget() {
  const [weather, setWeather] = useState<WeatherData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    const fetchWeather = async () => {
      try {
        setLoading(true);
        setError(null);

        const weatherData = await weatherService.getCurrentWeather();
        setWeather(weatherData);
      } catch (error: any) {
        console.error("Failed to fetch weather:", error);
        setError(error.message || "Failed to fetch weather data");

        // Only show toast for non-permission errors
        if (!error.message?.includes('denied') && !error.message?.includes('permission')) {
          toast({
            title: "Weather Update Failed",
            description: "Using default location. Check your internet connection.",
            variant: "destructive",
          });
        }

        // Fallback to basic weather data
        setWeather({
          temperature: 22,
          condition: "partly-cloudy",
          humidity: 65,
          windSpeed: 12,
          location: "Johannesburg, ZA",
          description: "Partly cloudy",
          icon: "02d",
          feelsLike: 24,
          pressure: 1013,
          visibility: 10,
          uvIndex: 5
        });
      } finally {
        setLoading(false);
      }
    };

    fetchWeather();

    // Refresh weather every 10 minutes
    const interval = setInterval(fetchWeather, 10 * 60 * 1000);

    return () => clearInterval(interval);
  }, [toast]);

  const getWeatherIcon = (condition: string) => {
    switch (condition) {
      case "sunny":
        return <Sun className="h-8 w-8 text-yellow-500" />;
      case "cloudy":
        return <Cloud className="h-8 w-8 text-gray-400" />;
      case "partly-cloudy":
        return <Cloud className="h-8 w-8 text-gray-300" />;
      case "rainy":
        return <CloudRain className="h-8 w-8 text-blue-500" />;
      case "snowy":
        return <CloudSnow className="h-8 w-8 text-blue-200" />;
      case "stormy":
        return <Zap className="h-8 w-8 text-purple-500" />;
      case "foggy":
        return <Eye className="h-8 w-8 text-gray-500" />;
      default:
        return <Sun className="h-8 w-8 text-yellow-500" />;
    }
  };

  if (loading) {
    return (
      <div className="vanta-card p-6 animate-pulse">
        <div className="flex items-center gap-4">
          <div className="w-12 h-12 bg-muted rounded-xl"></div>
          <div className="flex-1">
            <div className="h-4 bg-muted rounded mb-2"></div>
            <div className="h-3 bg-muted rounded w-2/3"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!weather) {
    return (
      <div className="vanta-card p-6">
        <div className="text-center text-muted-foreground">
          <Cloud className="h-8 w-8 mx-auto mb-2" />
          <p className="text-sm">Weather unavailable</p>
          {error && (
            <p className="text-xs text-red-500 mt-1">{error}</p>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="vanta-card p-6 bg-gradient-to-br from-blue-500/10 to-purple-500/5 border-blue-500/20">
      <div className="flex items-center gap-4">
        <div className="p-3 rounded-xl bg-blue-500/20">
          {getWeatherIcon(weather.condition)}
        </div>
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-1">
            <h3 className="font-semibold text-foreground">Weather</h3>
            <Thermometer className="h-4 w-4 text-blue-500" />
          </div>
          <p className="text-2xl font-bold text-foreground">{weather.temperature}°C</p>
          <p className="text-sm text-muted-foreground">{weather.location}</p>
          <p className="text-xs text-muted-foreground capitalize">{weather.description}</p>
        </div>
      </div>
      
      <div className="mt-4 grid grid-cols-2 gap-4 text-sm">
        <div className="flex items-center gap-2">
          <Wind className="h-4 w-4 text-blue-400" />
          <span className="text-muted-foreground">Wind: {weather.windSpeed} km/h</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 rounded-full bg-blue-400 flex items-center justify-center">
            <div className="w-2 h-2 rounded-full bg-white"></div>
          </div>
          <span className="text-muted-foreground">Humidity: {weather.humidity}%</span>
        </div>
        <div className="flex items-center gap-2">
          <Thermometer className="h-4 w-4 text-orange-400" />
          <span className="text-muted-foreground">Feels like: {weather.feelsLike}°C</span>
        </div>
        <div className="flex items-center gap-2">
          <Eye className="h-4 w-4 text-gray-400" />
          <span className="text-muted-foreground">Visibility: {weather.visibility} km</span>
        </div>
      </div>
    </div>
  );
}
