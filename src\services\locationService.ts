export interface LocationData {
  latitude: number;
  longitude: number;
  city: string;
  country: string;
  timezone: string;
  accuracy?: number;
}

export interface GeolocationPosition {
  coords: {
    latitude: number;
    longitude: number;
    accuracy: number;
  };
  timestamp: number;
}

class LocationService {
  private cachedLocation: LocationData | null = null;
  private cacheExpiry: number = 0;
  private readonly CACHE_DURATION = 30 * 60 * 1000; // 30 minutes

  async getCurrentLocation(): Promise<LocationData> {
    // Check cache first
    if (this.cachedLocation && Date.now() < this.cacheExpiry) {
      return this.cachedLocation;
    }

    try {
      // Try to get precise location from GPS
      const position = await this.getGeolocation();
      const locationData = await this.reverseGeocode(position.coords.latitude, position.coords.longitude);
      
      // Cache the result
      this.cachedLocation = {
        ...locationData,
        accuracy: position.coords.accuracy
      };
      this.cacheExpiry = Date.now() + this.CACHE_DURATION;
      
      return this.cachedLocation;
    } catch (error) {
      console.warn('GPS location failed, trying IP-based location:', error);
      
      try {
        // Fallback to IP-based location
        const ipLocation = await this.getLocationFromIP();
        
        // Cache the result
        this.cachedLocation = ipLocation;
        this.cacheExpiry = Date.now() + this.CACHE_DURATION;
        
        return this.cachedLocation;
      } catch (ipError) {
        console.error('All location methods failed:', ipError);
        
        // Final fallback to default location
        const defaultLocation = this.getDefaultLocation();
        return defaultLocation;
      }
    }
  }

  private async getGeolocation(): Promise<GeolocationPosition> {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error('Geolocation is not supported by this browser'));
        return;
      }

      navigator.geolocation.getCurrentPosition(
        (position) => {
          resolve({
            coords: {
              latitude: position.coords.latitude,
              longitude: position.coords.longitude,
              accuracy: position.coords.accuracy
            },
            timestamp: position.timestamp
          });
        },
        (error) => {
          let errorMessage = 'Unknown geolocation error';
          switch (error.code) {
            case error.PERMISSION_DENIED:
              errorMessage = 'User denied the request for Geolocation';
              break;
            case error.POSITION_UNAVAILABLE:
              errorMessage = 'Location information is unavailable';
              break;
            case error.TIMEOUT:
              errorMessage = 'The request to get user location timed out';
              break;
          }
          reject(new Error(errorMessage));
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 300000 // 5 minutes
        }
      );
    });
  }

  private async reverseGeocode(lat: number, lon: number): Promise<LocationData> {
    try {
      // Use a free geocoding service (you might want to use a more reliable one in production)
      const response = await fetch(
        `https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${lat}&longitude=${lon}&localityLanguage=en`
      );
      
      if (!response.ok) {
        throw new Error('Geocoding service unavailable');
      }
      
      const data = await response.json();
      
      return {
        latitude: lat,
        longitude: lon,
        city: data.city || data.locality || 'Unknown City',
        country: data.countryName || 'Unknown Country',
        timezone: this.getTimezoneFromCoordinates(lat, lon)
      };
    } catch (error) {
      console.error('Reverse geocoding failed:', error);
      throw new Error('Failed to get location details');
    }
  }

  private async getLocationFromIP(): Promise<LocationData> {
    try {
      // Use a free IP geolocation service
      const response = await fetch('https://ipapi.co/json/');
      
      if (!response.ok) {
        throw new Error('IP geolocation service unavailable');
      }
      
      const data = await response.json();
      
      if (data.error) {
        throw new Error(data.reason || 'IP geolocation failed');
      }
      
      return {
        latitude: data.latitude,
        longitude: data.longitude,
        city: data.city || 'Unknown City',
        country: data.country_name || 'Unknown Country',
        timezone: data.timezone || this.getTimezoneFromCoordinates(data.latitude, data.longitude)
      };
    } catch (error) {
      console.error('IP-based location failed:', error);
      throw new Error('Failed to get location from IP');
    }
  }

  private getTimezoneFromCoordinates(lat: number, lon: number): string {
    // Simple timezone estimation based on longitude
    // In a real app, you'd use a proper timezone API
    const timezoneOffset = Math.round(lon / 15);
    
    // Map some common locations to their timezones
    if (lat >= -35 && lat <= -22 && lon >= 16 && lon <= 33) {
      return 'Africa/Johannesburg'; // South Africa
    } else if (lat >= 24 && lat <= 49 && lon >= -125 && lon <= -66) {
      return 'America/New_York'; // USA (rough estimate)
    } else if (lat >= 49 && lat <= 61 && lon >= -8 && lon <= 2) {
      return 'Europe/London'; // UK
    } else if (lat >= 30 && lat <= 46 && lon >= 129 && lon <= 146) {
      return 'Asia/Tokyo'; // Japan
    }
    
    // Fallback to UTC offset estimation
    if (timezoneOffset >= -12 && timezoneOffset <= 12) {
      return `Etc/GMT${timezoneOffset >= 0 ? '-' : '+'}${Math.abs(timezoneOffset)}`;
    }
    
    return 'UTC';
  }

  private getDefaultLocation(): LocationData {
    // Default to Johannesburg, South Africa as specified in requirements
    return {
      latitude: -26.2041,
      longitude: 28.0473,
      city: 'Johannesburg',
      country: 'South Africa',
      timezone: 'Africa/Johannesburg'
    };
  }

  // Get location permission status
  async getPermissionStatus(): Promise<'granted' | 'denied' | 'prompt' | 'unsupported'> {
    if (!navigator.permissions || !navigator.geolocation) {
      return 'unsupported';
    }

    try {
      const permission = await navigator.permissions.query({ name: 'geolocation' });
      return permission.state as 'granted' | 'denied' | 'prompt';
    } catch (error) {
      console.warn('Could not check geolocation permission:', error);
      return 'unsupported';
    }
  }

  // Request location permission
  async requestLocationPermission(): Promise<boolean> {
    try {
      const position = await this.getGeolocation();
      return true;
    } catch (error) {
      console.warn('Location permission denied or failed:', error);
      return false;
    }
  }

  // Clear cached location (force refresh)
  clearCache(): void {
    this.cachedLocation = null;
    this.cacheExpiry = 0;
  }

  // Get distance between two points (in kilometers)
  getDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371; // Radius of the Earth in kilometers
    const dLat = this.deg2rad(lat2 - lat1);
    const dLon = this.deg2rad(lon2 - lon1);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) *
      Math.sin(dLon / 2) * Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c;
    return distance;
  }

  private deg2rad(deg: number): number {
    return deg * (Math.PI / 180);
  }

  // Check if location has changed significantly (more than 10km)
  hasLocationChanged(newLocation: LocationData): boolean {
    if (!this.cachedLocation) return true;
    
    const distance = this.getDistance(
      this.cachedLocation.latitude,
      this.cachedLocation.longitude,
      newLocation.latitude,
      newLocation.longitude
    );
    
    return distance > 10; // 10km threshold
  }

  // Get formatted location string
  getLocationString(location: LocationData): string {
    return `${location.city}, ${location.country}`;
  }
}

export const locationService = new LocationService();
