import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Calendar, CheckSquare, DollarSign, Target, Clock, TrendingUp, MapPin, Settings, Sun, Cloud, CloudRain } from "lucide-react";
import Layout from "@/components/Layout";
import AuthGuard from "@/components/AuthGuard";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { supabase } from "@/integrations/supabase/client";
import { taskService } from "@/services/taskService";
import { financeService } from "@/services/financeService";
import { formatCurrency } from "@/utils/currency";
import { weatherService } from "@/services/weatherService";

const Index = () => {
  const navigate = useNavigate();
  const [currentTime, setCurrentTime] = useState(new Date());
  const [weather, setWeather] = useState({ temp: 22, condition: 'sunny', location: 'Johannesburg' });
  const [greeting, setGreeting] = useState('');
  const [user, setUser] = useState<any>(null);
  const [dashboardStats, setDashboardStats] = useState({
    todaysTasks: 0,
    upcomingEvents: 0,
    balance: 0,
    todaysProgress: 0
  });
  const [loading, setLoading] = useState(true);
  const [todaysTasks, setTodaysTasks] = useState<any[]>([]);

  useEffect(() => {
    // Get current user
    const getCurrentUser = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      setUser(user);
    };

    getCurrentUser();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      setUser(session?.user ?? null);
    });

    return () => subscription.unsubscribe();
  }, []);

  useEffect(() => {
    // Update time every second
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    // Set greeting based on time
    const hour = new Date().getHours();
    if (hour < 12) setGreeting('Good morning');
    else if (hour < 17) setGreeting('Good afternoon');
    else setGreeting('Good evening');

    // Fetch real weather data
    fetchWeather();

    return () => clearInterval(timer);
  }, []);

  useEffect(() => {
    if (user?.id) {
      loadDashboardData();
    }
  }, [user?.id]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // Load today's tasks
      const todaysTasksData = await taskService.getTodaysTasks(user!.id);
      setTodaysTasks(todaysTasksData);

      // Load finance stats
      const financeStats = await financeService.getFinanceStats(user!.id);

      // Calculate today's progress (tasks completed today)
      const completedToday = todaysTasksData.filter(task => task.status === 'completed').length;
      const totalToday = todaysTasksData.length;
      const progress = totalToday > 0 ? (completedToday / totalToday) * 100 : 0;

      setDashboardStats({
        todaysTasks: totalToday,
        upcomingEvents: 2, // TODO: Get from calendar service when implemented
        balance: financeStats.netBalance,
        todaysProgress: progress
      });
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      // Keep default values
    } finally {
      setLoading(false);
    }
  };

  const fetchWeather = async () => {
    try {
      // Check if we need to request location permission
      const permissionStatus = await navigator.permissions?.query({ name: 'geolocation' });

      if (permissionStatus?.state === 'prompt') {
        // Show a user-friendly prompt before requesting location
        const userWantsLocation = window.confirm(
          'VANTA would like to access your location to provide accurate weather information. Allow location access?'
        );

        if (!userWantsLocation) {
          // Use default location
          const defaultLocation = import.meta.env.VITE_DEFAULT_LOCATION || 'Johannesburg, South Africa';
          setWeather({
            temp: 22,
            condition: 'sunny',
            location: defaultLocation.split(',')[0]
          });
          return;
        }
      }

      // Try to get weather with location
      const weatherData = await weatherService.getCurrentWeather();
      setWeather({
        temp: weatherData.temperature,
        condition: weatherData.condition,
        location: weatherData.location.split(',')[0]
      });
    } catch (error) {
      console.error('Weather fetch failed:', error);
      // Fallback to default location
      const defaultLocation = import.meta.env.VITE_DEFAULT_LOCATION || 'Johannesburg, South Africa';
      setWeather({
        temp: 22,
        condition: 'sunny',
        location: defaultLocation.split(',')[0]
      });
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      month: 'long',
      day: 'numeric'
    });
  };

  const getWeatherIcon = (condition: string) => {
    switch (condition) {
      case 'sunny': return <Sun className="w-5 h-5" />;
      case 'cloudy': return <Cloud className="w-5 h-5" />;
      case 'rainy': return <CloudRain className="w-5 h-5" />;
      default: return <Sun className="w-5 h-5" />;
    }
  };

  const toggleTodaysTask = (id: string) => {
    setTodaysTasks(prev => prev.map(task =>
      task.id === id ? { ...task, status: task.status === 'completed' ? 'pending' : 'completed' } : task
    ));
  };

  const todaysCompletedCount = todaysTasks.filter(task => task.status === 'completed').length;
  const todaysProgress = todaysTasks.length > 0 ? (todaysCompletedCount / todaysTasks.length) * 100 : 0;

  return (
    <AuthGuard>
      <Layout>
        <div className="space-y-8 vanta-fade-in max-w-7xl mx-auto">
          {/* Mobile-Optimized Header */}
          <div className="space-y-4">
            {/* Mobile: Logo at top, greeting below */}
            <div className="flex items-center justify-between md:hidden">
              <img
                src="/assets/VANTA Logo.png"
                alt="VANTA"
                className="h-8 object-contain"
              />
              <Button
                variant="ghost"
                size="sm"
                className="vanta-button"
                onClick={() => navigate('/settings')}
              >
                <Settings className="w-4 h-4" />
              </Button>
            </div>

            {/* Desktop: Greeting at top */}
            <div className="hidden md:flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-semibold text-foreground">
                  {greeting}
                </h1>
                <p className="text-muted-foreground mt-1">
                  {formatDate(currentTime)}
                </p>
              </div>
              <Button
                variant="ghost"
                size="sm"
                className="vanta-button"
                onClick={() => navigate('/settings')}
              >
                <Settings className="w-4 h-4" />
              </Button>
            </div>

            {/* Mobile: Greeting and time/weather */}
            <div className="md:hidden text-center">
              <h1 className="text-2xl font-semibold text-foreground mb-2">
                {greeting}
              </h1>
              <p className="text-muted-foreground text-sm mb-2">
                {formatDate(currentTime)}
              </p>
            </div>

            {/* Time and Weather - Both Mobile and Desktop */}
            <div className="flex items-center justify-center md:justify-end gap-4 text-sm">
              <div className="text-center md:text-right">
                <div className="text-xl md:text-2xl font-light text-foreground">
                  {formatTime(currentTime)}
                </div>
                <div className="flex items-center justify-center md:justify-end gap-2 text-muted-foreground mt-1">
                  {getWeatherIcon(weather.condition)}
                  <span>{weather.temp}°C</span>
                  <MapPin className="w-3 h-3" />
                  <span>{weather.location}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Clean Overview Cards */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Card className="vanta-stat-card">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-2xl font-semibold text-foreground">
                      {loading ? '...' : dashboardStats.todaysTasks}
                    </p>
                    <p className="text-sm text-muted-foreground">Tasks Today</p>
                  </div>
                  <CheckSquare className="vanta-icon" />
                </div>
              </CardContent>
            </Card>

            <Card className="vanta-stat-card">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-2xl font-semibold text-foreground">
                      {loading ? '...' : dashboardStats.upcomingEvents}
                    </p>
                    <p className="text-sm text-muted-foreground">Events</p>
                  </div>
                  <Calendar className="vanta-icon" />
                </div>
              </CardContent>
            </Card>

            <Card className="vanta-stat-card">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-2xl font-semibold text-foreground">
                      {loading ? '...' : formatCurrency(dashboardStats.balance)}
                    </p>
                    <p className="text-sm text-muted-foreground">Balance</p>
                  </div>
                  <DollarSign className="vanta-icon" />
                </div>
              </CardContent>
            </Card>

            <Card className="vanta-stat-card">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-2xl font-semibold text-foreground">
                      {loading ? '...' : Math.round(dashboardStats.todaysProgress)}%
                    </p>
                    <p className="text-sm text-muted-foreground">Today's Progress</p>
                  </div>
                  <TrendingUp className="vanta-icon" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Today's Focus */}
          <div className="space-y-4">
            <h2 className="text-xl font-semibold text-foreground vanta-title">Today's Focus</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card className="vanta-card">
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h3 className="font-medium text-foreground">Pending Tasks</h3>
                      <CheckSquare className="vanta-icon" />
                    </div>
                    <div className="space-y-3">
                      {todaysTasks.filter(task => task.status !== 'completed').map(task => (
                        <div key={task.id} className="flex items-center gap-3 group cursor-pointer" onClick={() => toggleTodaysTask(task.id)}>
                          <div className="w-4 h-4 border-2 border-primary rounded-sm flex items-center justify-center hover:bg-primary/20 transition-colors">
                            {task.status === 'completed' && <CheckSquare className="w-3 h-3 text-primary" />}
                          </div>
                          <span className={`text-sm transition-colors ${task.status === 'completed' ? 'line-through text-muted-foreground' : 'text-foreground'}`}>
                            {task.title}
                          </span>
                        </div>
                      ))}
                      {todaysTasks.filter(task => task.status !== 'completed').length === 0 && (
                        <p className="text-sm text-muted-foreground text-center py-4">All tasks completed! 🎉</p>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="vanta-card">
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h3 className="font-medium text-foreground">Upcoming Events</h3>
                      <Calendar className="vanta-icon" />
                    </div>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-foreground">Team meeting</span>
                        <span className="text-xs text-muted-foreground">Tomorrow 2PM</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-foreground">Mom's birthday</span>
                        <span className="text-xs text-muted-foreground">Friday</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Daily Affirmation */}
          <Card className="vanta-card">
            <CardContent className="p-8 text-center">
              <p className="text-lg text-foreground font-medium mb-2">
                "Success is not final, failure is not fatal: it is the courage to continue that counts."
              </p>
              <p className="text-sm text-muted-foreground">Today's Affirmation</p>
            </CardContent>
          </Card>
        </div>
      </Layout>
    </AuthGuard>
  );
};

export default Index;
