import { ReactNode } from "react";
import { LucideIcon } from "lucide-react";

interface DashboardCardProps {
  title: string;
  value?: string | number;
  subtitle?: string;
  icon: LucideIcon;
  gradient?: boolean;
  children?: ReactNode;
  className?: string;
}

export default function DashboardCard({
  title,
  value,
  subtitle,
  icon: Icon,
  gradient = false,
  children,
  className = ""
}: DashboardCardProps) {
  return (
    <div className={`
      vanta-card group relative overflow-hidden p-6 cursor-pointer
      ${gradient
        ? "bg-gradient-to-br from-primary/20 to-purple-500/10 border-primary/20 shadow-lg"
        : "hover:border-primary/30"
      }
      ${className}
    `}>
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center gap-3 mb-4">
            <div className={`
              p-3 rounded-xl transition-all duration-300 group-hover:scale-110
              ${gradient
                ? "bg-primary/20 text-primary shadow-lg"
                : "bg-primary/10 text-primary"
              }
            `}>
              <Icon className="h-5 w-5" />
            </div>
            <h3 className="font-semibold text-foreground group-hover:text-primary transition-colors">{title}</h3>
          </div>
          
          {value && (
            <div className="mb-2">
              <p className="text-2xl font-bold text-foreground">{value}</p>
            </div>
          )}
          
          {subtitle && (
            <p className="text-sm text-muted-foreground">{subtitle}</p>
          )}
          
          {children && (
            <div className="mt-4">
              {children}
            </div>
          )}
        </div>
      </div>
      
      {/* Enhanced glow effect for gradient cards */}
      {gradient && (
        <>
          <div className="absolute inset-0 -z-10 bg-gradient-to-br from-primary/10 to-purple-500/5 rounded-2xl blur-xl opacity-50" />
          <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-primary/20 to-transparent rounded-full blur-2xl -translate-y-8 translate-x-8" />
        </>
      )}
    </div>
  );
}