-- Finance system improvements for income/expense tracking
-- Add new transaction types for proper financial management

-- Extend finance_type enum to include income and expense
ALTER TYPE finance_type ADD VALUE 'income';
ALTER TYPE finance_type ADD VALUE 'expense';

-- Add new columns for enhanced finance tracking
ALTER TABLE finance_items 
ADD COLUMN transaction_type TEXT DEFAULT 'manual',
ADD COLUMN recurring BOOLEAN DEFAULT FALSE,
ADD COLUMN recurring_frequency TEXT,
ADD COLUMN recurring_end_date TIMESTAMP WITH TIME ZONE,
ADD COLUMN next_occurrence TIMESTAMP WITH TIME ZONE,
ADD COLUMN is_scheduled BOOLEAN DEFAULT FALSE,
ADD COLUMN auto_pay BOOLEAN DEFAULT FALSE;

-- Make person_name optional for income/expense transactions
ALTER TABLE finance_items ALTER COLUMN person_name DROP NOT NULL;

-- Create index for recurring transactions
CREATE INDEX idx_finance_items_recurring ON finance_items(recurring, next_occurrence) WHERE recurring = true;
CREATE INDEX idx_finance_items_transaction_type ON finance_items(transaction_type);

-- <PERSON><PERSON> function to handle recurring transactions
CREATE OR REPLACE FUNCTION process_recurring_transactions()
<PERSON><PERSON><PERSON>NS void AS $$
DECLARE
    rec RECORD;
    next_date TIMESTAMP WITH TIME ZONE;
BEGIN
    -- Process all due recurring transactions
    FOR rec IN 
        SELECT * FROM finance_items 
        WHERE recurring = true 
        AND next_occurrence <= NOW()
        AND (recurring_end_date IS NULL OR recurring_end_date > NOW())
    LOOP
        -- Calculate next occurrence based on frequency
        CASE rec.recurring_frequency
            WHEN 'daily' THEN
                next_date := rec.next_occurrence + INTERVAL '1 day';
            WHEN 'weekly' THEN
                next_date := rec.next_occurrence + INTERVAL '1 week';
            WHEN 'monthly' THEN
                next_date := rec.next_occurrence + INTERVAL '1 month';
            WHEN 'yearly' THEN
                next_date := rec.next_occurrence + INTERVAL '1 year';
            ELSE
                next_date := rec.next_occurrence + INTERVAL '1 month';
        END CASE;

        -- Create new transaction for the current occurrence
        INSERT INTO finance_items (
            user_id, description, amount, type, person_name, person_email, person_phone,
            category, priority, due_date, payment_method, notes, transaction_type,
            recurring, recurring_frequency, recurring_end_date, next_occurrence,
            is_scheduled, auto_pay
        ) VALUES (
            rec.user_id, rec.description, rec.amount, rec.type, rec.person_name, 
            rec.person_email, rec.person_phone, rec.category, rec.priority, 
            rec.next_occurrence, rec.payment_method, rec.notes, 'recurring',
            false, null, null, null, true, rec.auto_pay
        );

        -- Update the template record with next occurrence
        UPDATE finance_items 
        SET next_occurrence = next_date,
            updated_at = NOW()
        WHERE id = rec.id;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically process recurring transactions daily
-- This would typically be called by a cron job or scheduled function
