import axios from 'axios';
import { locationService, type LocationData } from './locationService';
import { errorHandler } from '@/utils/errorHandler';

export interface WeatherData {
  temperature: number;
  condition: string;
  humidity: number;
  windSpeed: number;
  location: string;
  description: string;
  icon: string;
  feelsLike: number;
  pressure: number;
  visibility: number;
  uvIndex: number;
}

export interface LocationData {
  lat: number;
  lon: number;
  city: string;
  country: string;
}

class WeatherService {
  private readonly API_KEY = import.meta.env.VITE_OPENWEATHER_API_KEY;
  private readonly BASE_URL = 'https://api.openweathermap.org/data/2.5';
  private readonly GEO_URL = 'https://api.openweathermap.org/geo/1.0';

  async getCurrentLocation(): Promise<LocationData> {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error('Geolocation is not supported by this browser'));
        return;
      }

      navigator.geolocation.getCurrentPosition(
        async (position) => {
          try {
            const { latitude, longitude } = position.coords;
            const locationData = await this.reverseGeocode(latitude, longitude);
            resolve(locationData);
          } catch (error) {
            reject(error);
          }
        },
        (error) => {
          reject(new Error(`Geolocation error: ${error.message}`));
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 300000 // 5 minutes
        }
      );
    });
  }

  async reverseGeocode(lat: number, lon: number): Promise<LocationData> {
    try {
      const response = await axios.get(
        `${this.GEO_URL}/reverse?lat=${lat}&lon=${lon}&limit=1&appid=${this.API_KEY}`
      );

      if (response.data && response.data.length > 0) {
        const location = response.data[0];
        return {
          lat,
          lon,
          city: location.name,
          country: location.country
        };
      }

      throw new Error('Location not found');
    } catch (error) {
      console.error('Reverse geocoding failed:', error);
      throw new Error('Failed to get location details');
    }
  }

  async geocodeLocation(locationName: string): Promise<LocationData> {
    try {
      const response = await axios.get(
        `${this.GEO_URL}/direct?q=${encodeURIComponent(locationName)}&limit=1&appid=${this.API_KEY}`
      );

      if (response.data && response.data.length > 0) {
        const location = response.data[0];
        return {
          lat: location.lat,
          lon: location.lon,
          city: location.name,
          country: location.country
        };
      }

      throw new Error('Location not found');
    } catch (error) {
      console.error('Geocoding failed:', error);
      throw new Error('Failed to find location');
    }
  }

  async getWeatherByCoordinates(lat: number, lon: number): Promise<WeatherData> {
    try {
      if (!this.API_KEY) {
        throw new Error('OpenWeather API key not configured. Please add VITE_OPENWEATHER_API_KEY to your environment variables.');
      }

      const response = await errorHandler.retry(
        () => axios.get(
          `${this.BASE_URL}/weather?lat=${lat}&lon=${lon}&appid=${this.API_KEY}&units=metric`,
          { timeout: 10000 }
        ),
        3,
        1000
      );

      return this.parseWeatherData(response.data);
    } catch (error: any) {
      const message = errorHandler.handleNetworkError(error, 'weatherService.getWeatherByCoordinates');
      errorHandler.logError({
        code: 'WEATHER_API_ERROR',
        message: 'Failed to fetch weather data',
        details: { error, lat, lon },
        timestamp: new Date(),
        context: 'weatherService.getWeatherByCoordinates'
      });
      throw new Error(message);
    }
  }

  async getWeatherByLocation(locationName: string): Promise<WeatherData> {
    try {
      const locationData = await this.geocodeLocation(locationName);
      return await this.getWeatherByCoordinates(locationData.lat, locationData.lon);
    } catch (error) {
      console.error('Weather by location error:', error);
      throw error;
    }
  }

  async getCurrentWeather(): Promise<WeatherData> {
    try {
      // Try to get current location first
      const location = await locationService.getCurrentLocation();
      return await this.getWeatherByCoordinates(location.latitude, location.longitude);
    } catch (error) {
      console.warn('Failed to get current location, using default:', error);
      // Fallback to default location from settings
      const defaultLocation = import.meta.env.VITE_DEFAULT_LOCATION || 'Johannesburg, South Africa';
      return await this.getWeatherByLocation(defaultLocation);
    }
  }

  private parseWeatherData(data: any): WeatherData {
    const condition = this.mapWeatherCondition(data.weather[0].main, data.weather[0].id);
    
    return {
      temperature: Math.round(data.main.temp),
      condition,
      humidity: data.main.humidity,
      windSpeed: Math.round(data.wind?.speed * 3.6) || 0, // Convert m/s to km/h
      location: `${data.name}, ${data.sys.country}`,
      description: data.weather[0].description,
      icon: data.weather[0].icon,
      feelsLike: Math.round(data.main.feels_like),
      pressure: data.main.pressure,
      visibility: Math.round((data.visibility || 10000) / 1000), // Convert to km
      uvIndex: 0 // Would need separate UV API call
    };
  }

  private mapWeatherCondition(main: string, id: number): string {
    // Map OpenWeather conditions to our app's condition types
    switch (main.toLowerCase()) {
      case 'clear':
        return 'sunny';
      case 'clouds':
        return id < 803 ? 'partly-cloudy' : 'cloudy';
      case 'rain':
      case 'drizzle':
        return 'rainy';
      case 'snow':
        return 'snowy';
      case 'thunderstorm':
        return 'stormy';
      case 'mist':
      case 'fog':
      case 'haze':
        return 'foggy';
      default:
        return 'partly-cloudy';
    }
  }

  // Get weather forecast (5-day)
  async getWeatherForecast(lat?: number, lon?: number): Promise<any[]> {
    try {
      if (!this.API_KEY) {
        throw new Error('OpenWeather API key not configured');
      }

      let coordinates = { lat, lon };

      if (!lat || !lon) {
        const location = await locationService.getCurrentLocation();
        coordinates = { lat: location.latitude, lon: location.longitude };
      }

      const response = await axios.get(
        `${this.BASE_URL}/forecast?lat=${coordinates.lat}&lon=${coordinates.lon}&appid=${this.API_KEY}&units=metric`
      );

      return response.data.list.map((item: any) => ({
        date: new Date(item.dt * 1000),
        temperature: Math.round(item.main.temp),
        condition: this.mapWeatherCondition(item.weather[0].main, item.weather[0].id),
        description: item.weather[0].description,
        humidity: item.main.humidity,
        windSpeed: Math.round(item.wind?.speed * 3.6) || 0
      }));
    } catch (error) {
      console.error('Weather forecast error:', error);
      throw new Error('Failed to fetch weather forecast');
    }
  }
}

export const weatherService = new WeatherService();
