import { useState, useEffect } from "react";
import { Plus, Search, Pin, Archive, Lock, Unlock, Shield } from "lucide-react";
import Layout from "@/components/Layout";
import AuthGuard from "@/components/AuthGuard";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";

interface Note {
  id: string;
  title: string;
  content: string;
  pinned: boolean;
  archived: boolean;
  pinProtected: boolean;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
}

export default function Notes() {
  const [pin, setPin] = useState("");
  const [storedPin, setStoredPin] = useState<string | null>(null);
  const [isSettingPin, setIsSettingPin] = useState(false);
  const [newPin, setNewPin] = useState("");
  const [confirmPin, setConfirmPin] = useState("");
  const [pinError, setPinError] = useState("");
  const [lockedNotes, setLockedNotes] = useState<Set<string>>(new Set());

  const [notes, setNotes] = useState<Note[]>([
    {
      id: "1",
      title: "Welcome to VANTA Notes",
      content: "This is your secure note-taking space. You can protect individual notes with a PIN for extra security.",
      pinned: true,
      archived: false,
      pinProtected: false,
      tags: ["welcome", "getting-started"],
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ]);

  const [searchTerm, setSearchTerm] = useState("");
  const [showAddForm, setShowAddForm] = useState(false);
  const [selectedNote, setSelectedNote] = useState<Note | null>(null);
  const [newNoteTitle, setNewNoteTitle] = useState("");
  const [newNoteContent, setNewNoteContent] = useState("");

  // Load PIN from localStorage on component mount
  useEffect(() => {
    const savedPin = localStorage.getItem('vanta-notes-pin');
    setStoredPin(savedPin); // Can be null if no PIN is set
  }, []);

  const handlePinSubmit = (noteId: string) => {
    if (pin === storedPin) {
      setLockedNotes(prev => {
        const newSet = new Set(prev);
        newSet.delete(noteId);
        return newSet;
      });
      setPin("");
      setPinError("");
    } else {
      setPinError("Incorrect PIN. Please try again.");
      setPin("");
    }
  };

  const handleSetNewPin = () => {
    if (newPin.length !== 4 || confirmPin.length !== 4) {
      setPinError("PIN must be 4 digits");
      return;
    }
    if (newPin !== confirmPin) {
      setPinError("PINs don't match");
      return;
    }
    setStoredPin(newPin);
    localStorage.setItem('vanta-notes-pin', newPin);
    setIsSettingPin(false);
    setNewPin("");
    setConfirmPin("");
    setPinError("");
  };

  const toggleNotePinProtection = (noteId: string) => {
    if (!storedPin) {
      // If no PIN is set, prompt user to set one
      setIsSettingPin(true);
      return;
    }

    // Toggle PIN protection for this note
    // In a real app, this would update the note in the database
    console.log(`Toggle PIN protection for note ${noteId}`);
  };

  const handleAddNote = () => {
    if (!newNoteTitle.trim() || !newNoteContent.trim()) return;

    const newNote: Note = {
      id: Date.now().toString(),
      title: newNoteTitle,
      content: newNoteContent,
      pinned: false,
      archived: false,
      pinProtected: false,
      tags: [],
      createdAt: new Date(),
      updatedAt: new Date()
    };

    setNotes(prev => [newNote, ...prev]);
    setNewNoteTitle("");
    setNewNoteContent("");
    setShowAddForm(false);
  };

  const isNoteAccessible = (note: Note) => {
    if (!note.pinProtected) return true;
    if (!storedPin) return true; // If no PIN is set, all notes are accessible
    return !lockedNotes.has(note.id);
  };

  const renderNoteCard = (note: Note) => {
    const isAccessible = isNoteAccessible(note);

    return (
      <Card key={note.id} className="vanta-card hover:shadow-lg transition-all duration-200 group">
        <CardContent className="p-4">
          <div className="flex items-start justify-between mb-3">
            <h3 className="font-medium text-foreground truncate flex-1">
              {isAccessible ? note.title : '🔒 Protected Note'}
            </h3>
            <div className="flex items-center gap-1 ml-2">
              {note.pinProtected && (
                <Lock className="w-3 h-3 text-muted-foreground" />
              )}
              {note.pinned && (
                <Pin className="w-3 h-3 text-primary" />
              )}
            </div>
          </div>

          <p className="text-sm text-muted-foreground line-clamp-3 mb-3">
            {isAccessible ? note.content : 'This note is protected with a PIN. Click to unlock.'}
          </p>

          {note.tags.length > 0 && (
            <div className="flex flex-wrap gap-1 mb-3">
              {note.tags.slice(0, 3).map(tag => (
                <span key={tag} className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">
                  {tag}
                </span>
              ))}
              {note.tags.length > 3 && (
                <span className="text-xs text-muted-foreground">+{note.tags.length - 3}</span>
              )}
            </div>
          )}

          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <span>{note.createdAt.toLocaleDateString()}</span>
            <div className="flex items-center gap-2">
              {!isAccessible && (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => {
                    // Show PIN entry for this specific note
                    const enteredPin = prompt('Enter PIN to unlock this note:');
                    if (enteredPin === storedPin) {
                      setLockedNotes(prev => {
                        const newSet = new Set(prev);
                        newSet.delete(note.id);
                        return newSet;
                      });
                    }
                  }}
                  className="text-xs"
                >
                  <Unlock className="w-3 h-3" />
                </Button>
              )}
              <Button
                size="sm"
                variant="ghost"
                onClick={() => toggleNotePinProtection(note.id)}
                className="text-xs"
              >
                {note.pinProtected ? <Lock className="w-3 h-3" /> : <Unlock className="w-3 h-3" />}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  const lockAllProtectedNotes = () => {
    // Lock all PIN-protected notes
    const protectedNoteIds = notes.filter(note => note.pinProtected).map(note => note.id);
    setLockedNotes(new Set(protectedNoteIds));
  };

  const filteredNotes = notes.filter(note =>
    note.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    note.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
    note.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const pinnedNotes = filteredNotes.filter(note => note.pinned && !note.archived);
  const regularNotes = filteredNotes.filter(note => !note.pinned && !note.archived);

  return (
    <AuthGuard>
      <Layout>
        <div className="space-y-6 vanta-fade-in">
          {/* Header */}
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
            <div>
              <h1 className="text-3xl font-semibold text-foreground">Notes</h1>
              <p className="text-muted-foreground mt-1">
                Secure notes with optional PIN protection
              </p>
            </div>
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                className="gap-2"
                onClick={() => setIsSettingPin(true)}
              >
                <Shield className="w-4 h-4" />
                {storedPin ? 'Change PIN' : 'Set PIN'}
              </Button>
              {storedPin && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={lockAllProtectedNotes}
                  className="gap-2"
                >
                  <Lock className="w-4 h-4" />
                  Lock Protected
                </Button>
              )}
              <Button
                size="sm"
                className="gap-2 vanta-button"
                onClick={() => setShowAddForm(!showAddForm)}
              >
                <Plus className="w-4 h-4" />
                Add Note
              </Button>
            </div>
          </div>

          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Search notes..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 vanta-input"
            />
          </div>

          {/* Notes Display */}
          <div className="space-y-6">
            {notes.length === 0 ? (
              <div className="text-center py-12">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-muted flex items-center justify-center">
                  <Plus className="w-8 h-8 text-muted-foreground" />
                </div>
                <h3 className="text-lg font-medium text-foreground mb-2">No notes yet</h3>
                <p className="text-muted-foreground mb-4">Create your first note to get started</p>
                <Button onClick={() => setShowAddForm(true)} className="vanta-button">
                  <Plus className="w-4 h-4 mr-2" />
                  Add Your First Note
                </Button>
              </div>
            ) : (
              <>
                {pinnedNotes.length > 0 && (
                  <div>
                    <h2 className="text-lg font-medium text-foreground mb-3 flex items-center gap-2">
                      <Pin className="w-4 h-4" />
                      Pinned Notes
                    </h2>
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                      {pinnedNotes.map(note => renderNoteCard(note))}
                    </div>
                  </div>
                )}

                {regularNotes.length > 0 && (
                  <div>
                    <h2 className="text-lg font-medium text-foreground mb-3">All Notes</h2>
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                      {regularNotes.map(note => renderNoteCard(note))}
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
        </div>

        {/* PIN Setup Modal */}
        {isSettingPin && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
            <Card className="w-full max-w-md mx-4 vanta-card">
              <CardHeader className="text-center">
                <CardTitle className="text-xl font-semibold text-foreground">
                  {storedPin ? 'Change PIN' : 'Set PIN'}
                </CardTitle>
                <p className="text-muted-foreground mt-2">
                  {storedPin ? 'Enter a new 4-digit PIN' : 'Create a 4-digit PIN to protect your notes'}
                </p>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Input
                    type="password"
                    placeholder="New PIN"
                    value={newPin}
                    onChange={(e) => {
                      const value = e.target.value.replace(/\D/g, '').slice(0, 4);
                      setNewPin(value);
                      setPinError("");
                    }}
                    className="text-center text-2xl tracking-widest vanta-input"
                    maxLength={4}
                  />
                </div>
                <div className="space-y-2">
                  <Input
                    type="password"
                    placeholder="Confirm PIN"
                    value={confirmPin}
                    onChange={(e) => {
                      const value = e.target.value.replace(/\D/g, '').slice(0, 4);
                      setConfirmPin(value);
                      setPinError("");
                    }}
                    className="text-center text-2xl tracking-widest vanta-input"
                    maxLength={4}
                  />
                  {pinError && (
                    <p className="text-sm text-destructive text-center">{pinError}</p>
                  )}
                </div>
                <div className="flex gap-2">
                  <Button
                    onClick={handleSetNewPin}
                    disabled={newPin.length !== 4 || confirmPin.length !== 4}
                  className="flex-1 vanta-button"
                >
                  Set PIN
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    setIsSettingPin(false);
                    setNewPin("");
                    setConfirmPin("");
                    setPinError("");
                  }}
                  className="flex-1"
                >
                  Cancel
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

        {/* Add Note Modal */}
        {showAddForm && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
            <Card className="w-full max-w-2xl mx-4 vanta-card">
              <CardHeader>
                <CardTitle className="text-xl font-semibold text-foreground">Add New Note</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Input
                    placeholder="Note title..."
                    value={newNoteTitle}
                    onChange={(e) => setNewNoteTitle(e.target.value)}
                    className="vanta-input"
                  />
                </div>
                <div>
                  <Textarea
                    placeholder="Write your note content here..."
                    value={newNoteContent}
                    onChange={(e) => setNewNoteContent(e.target.value)}
                    className="vanta-input min-h-[200px]"
                  />
                </div>
                <div className="flex gap-3 justify-end">
                  <Button
                    variant="outline"
                    onClick={() => {
                      setShowAddForm(false);
                      setNewNoteTitle("");
                      setNewNoteContent("");
                    }}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleAddNote}
                    className="vanta-button"
                    disabled={!newNoteTitle.trim() || !newNoteContent.trim()}
                  >
                    Add Note
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
    </Layout>
  </AuthGuard>
);

}