import { useState } from "react";
import {
  Plus,
  Check,
  Trash2,
  ShoppingCart,
  Search,
  Filter,
  Edit,
  Copy,
  Share,
  MapPin,
  Clock,
  DollarSign,
  Star,
  Archive,
  MoreHorizontal,
  CheckCircle,
  Circle,
  Package,
  Tag,
  Calendar,
  TrendingUp
} from "lucide-react";
import Layout from "@/components/Layout";
import AuthGuard from "@/components/AuthGuard";
import { Button } from "@/components/ui/button";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { format } from "date-fns";

interface ShoppingItem {
  id: string;
  name: string;
  category: string;
  completed: boolean;
  quantity: number;
  unit?: string;
  estimatedPrice?: number;
  actualPrice?: number;
  notes?: string;
  priority: "low" | "medium" | "high";
  brand?: string;
  store?: string;
  addedDate: Date;
  completedDate?: Date;
}

interface ShoppingList {
  id: string;
  name: string;
  description?: string;
  items: ShoppingItem[];
  store?: string;
  budget?: number;
  createdDate: Date;
  lastModified: Date;
  archived: boolean;
  shared: boolean;
  color: string;
}

export default function Shopping() {
  const [lists, setLists] = useState<ShoppingList[]>([
    {
      id: "1",
      name: "Weekly Groceries",
      description: "Regular grocery shopping for the week",
      store: "Whole Foods",
      budget: 150,
      color: "bg-green-500",
      createdDate: new Date(2024, 11, 10),
      lastModified: new Date(2024, 11, 15),
      archived: false,
      shared: false,
      items: [
        {
          id: "1",
          name: "Organic Milk",
          category: "Dairy",
          completed: false,
          quantity: 2,
          unit: "gallons",
          estimatedPrice: 125,
          priority: "high",
          brand: "Organic Valley",
          addedDate: new Date(2024, 11, 10)
        },
        {
          id: "2",
          name: "Sourdough Bread",
          category: "Bakery",
          completed: true,
          quantity: 1,
          unit: "loaf",
          estimatedPrice: 80,
          actualPrice: 75,
          priority: "medium",
          addedDate: new Date(2024, 11, 10),
          completedDate: new Date(2024, 11, 15)
        },
        {
          id: "3",
          name: "Honeycrisp Apples",
          category: "Produce",
          completed: false,
          quantity: 6,
          unit: "pieces",
          estimatedPrice: 140,
          priority: "medium",
          notes: "Look for organic if available",
          addedDate: new Date(2024, 11, 10)
        },
        {
          id: "4",
          name: "Free-range Chicken Breast",
          category: "Meat",
          completed: false,
          quantity: 2,
          unit: "lbs",
          estimatedPrice: 230,
          priority: "high",
          brand: "Bell & Evans",
          addedDate: new Date(2024, 11, 10)
        },
      ],
    },
    {
      id: "2",
      name: "Tech Essentials",
      description: "Electronics and accessories needed",
      store: "Best Buy",
      budget: 200,
      color: "bg-blue-500",
      createdDate: new Date(2024, 11, 12),
      lastModified: new Date(2024, 11, 12),
      archived: false,
      shared: true,
      items: [
        {
          id: "5",
          name: "USB-C Phone Charger",
          category: "Electronics",
          completed: false,
          quantity: 1,
          unit: "piece",
          estimatedPrice: 460,
          priority: "high",
          brand: "Anker",
          addedDate: new Date(2024, 11, 12)
        },
        {
          id: "6",
          name: "Wireless Bluetooth Headphones",
          category: "Electronics",
          completed: false,
          quantity: 1,
          unit: "piece",
          estimatedPrice: 1600,
          priority: "medium",
          brand: "Sony",
          notes: "Check for noise cancellation feature",
          addedDate: new Date(2024, 11, 12)
        },
      ],
    },
  ]);

  const [newItem, setNewItem] = useState("");
  const [selectedList, setSelectedList] = useState("1");
  const [searchQuery, setSearchQuery] = useState("");
  const [filterCategory, setFilterCategory] = useState("all");
  const [filterPriority, setFilterPriority] = useState("all");
  const [showCompleted, setShowCompleted] = useState(true);
  const [activeTab, setActiveTab] = useState("lists");

  const categories = [
    { name: "Produce", icon: "🥬", color: "text-green-500" },
    { name: "Dairy", icon: "🥛", color: "text-blue-500" },
    { name: "Meat", icon: "🥩", color: "text-red-500" },
    { name: "Bakery", icon: "🍞", color: "text-yellow-500" },
    { name: "Electronics", icon: "📱", color: "text-purple-500" },
    { name: "Household", icon: "🧽", color: "text-orange-500" },
    { name: "Personal Care", icon: "🧴", color: "text-pink-500" },
    { name: "Snacks", icon: "🍿", color: "text-indigo-500" },
    { name: "Beverages", icon: "🥤", color: "text-cyan-500" },
    { name: "Other", icon: "📦", color: "text-gray-500" }
  ];

  const smartCategorize = (itemName: string): string => {
    const name = itemName.toLowerCase();
    if (name.includes("milk") || name.includes("cheese") || name.includes("yogurt") || name.includes("butter")) return "Dairy";
    if (name.includes("apple") || name.includes("banana") || name.includes("lettuce") || name.includes("tomato") || name.includes("carrot")) return "Produce";
    if (name.includes("chicken") || name.includes("beef") || name.includes("pork") || name.includes("fish") || name.includes("meat")) return "Meat";
    if (name.includes("bread") || name.includes("bagel") || name.includes("muffin") || name.includes("cake")) return "Bakery";
    if (name.includes("phone") || name.includes("charger") || name.includes("headphone") || name.includes("cable")) return "Electronics";
    if (name.includes("soap") || name.includes("shampoo") || name.includes("toothpaste") || name.includes("deodorant")) return "Personal Care";
    if (name.includes("chips") || name.includes("cookies") || name.includes("candy") || name.includes("snack")) return "Snacks";
    if (name.includes("water") || name.includes("juice") || name.includes("soda") || name.includes("coffee") || name.includes("tea")) return "Beverages";
    if (name.includes("detergent") || name.includes("cleaner") || name.includes("paper towel") || name.includes("toilet paper")) return "Household";
    return "Other";
  };

  const addItem = () => {
    if (!newItem.trim()) return;

    const item: ShoppingItem = {
      id: Date.now().toString(),
      name: newItem,
      category: smartCategorize(newItem),
      completed: false,
      quantity: 1,
      priority: "medium",
      addedDate: new Date(),
    };

    setLists(prev => prev.map(list =>
      list.id === selectedList
        ? {
            ...list,
            items: [...list.items, item],
            lastModified: new Date()
          }
        : list
    ));
    setNewItem("");
  };

  const toggleItem = (listId: string, itemId: string) => {
    setLists(prev => prev.map(list =>
      list.id === listId
        ? {
            ...list,
            items: list.items.map(item =>
              item.id === itemId
                ? {
                    ...item,
                    completed: !item.completed,
                    completedDate: !item.completed ? new Date() : undefined
                  }
                : item
            ),
            lastModified: new Date()
          }
        : list
    ));
  };

  const deleteItem = (listId: string, itemId: string) => {
    setLists(prev => prev.map(list =>
      list.id === listId
        ? {
            ...list,
            items: list.items.filter(item => item.id !== itemId),
            lastModified: new Date()
          }
        : list
    ));
  };

  const getFilteredItems = (items: ShoppingItem[]) => {
    return items.filter(item => {
      const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           item.category.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           item.brand?.toLowerCase().includes(searchQuery.toLowerCase());

      const matchesCategory = filterCategory === "all" || item.category === filterCategory;
      const matchesPriority = filterPriority === "all" || item.priority === filterPriority;
      const matchesCompleted = showCompleted || !item.completed;

      return matchesSearch && matchesCategory && matchesPriority && matchesCompleted;
    });
  };

  const getListStats = (list: ShoppingList) => {
    const totalItems = list.items.length;
    const completedItems = list.items.filter(item => item.completed).length;
    const totalEstimated = list.items.reduce((sum, item) => sum + (item.estimatedPrice || 0), 0);
    const totalActual = list.items.reduce((sum, item) => sum + (item.actualPrice || item.estimatedPrice || 0), 0);
    const progress = totalItems > 0 ? (completedItems / totalItems) * 100 : 0;

    return {
      totalItems,
      completedItems,
      totalEstimated,
      totalActual,
      progress,
      remainingBudget: (list.budget || 0) - totalActual
    };
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high": return "text-red-500 bg-red-500/10 border-red-500/30";
      case "medium": return "text-yellow-500 bg-yellow-500/10 border-yellow-500/30";
      case "low": return "text-green-500 bg-green-500/10 border-green-500/30";
      default: return "text-gray-500 bg-gray-500/10 border-gray-500/30";
    }
  };

  return (
    <AuthGuard>
      <Layout>
        <div className="space-y-6 vanta-fade-in">
          {/* Header */}
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-foreground to-green-500 bg-clip-text text-transparent">
                Smart Shopping
              </h1>
              <p className="text-muted-foreground mt-1">
                Organize your shopping with intelligent lists and budget tracking
              </p>
            </div>
            <div className="flex items-center gap-3">
              <Button variant="outline" size="sm" className="gap-2">
                <Archive className="h-4 w-4" />
                Archived
              </Button>
              <Button size="sm" className="gap-2 vanta-button">
                <Plus className="h-4 w-4" />
                New List
              </Button>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="vanta-card p-4 bg-gradient-to-br from-green-500/10 to-emerald-500/5 border-green-500/20">
              <div className="flex items-center gap-3">
                <ShoppingCart className="h-5 w-5 text-green-500" />
                <div>
                  <p className="text-lg font-bold text-green-500">{lists.length}</p>
                  <p className="text-xs text-muted-foreground">Active Lists</p>
                </div>
              </div>
            </div>

            <div className="vanta-card p-4 bg-gradient-to-br from-blue-500/10 to-cyan-500/5 border-blue-500/20">
              <div className="flex items-center gap-3">
                <Package className="h-5 w-5 text-blue-500" />
                <div>
                  <p className="text-lg font-bold text-blue-500">
                    {lists.reduce((sum, list) => sum + list.items.length, 0)}
                  </p>
                  <p className="text-xs text-muted-foreground">Total Items</p>
                </div>
              </div>
            </div>

            <div className="vanta-card p-4 bg-gradient-to-br from-yellow-500/10 to-orange-500/5 border-yellow-500/20">
              <div className="flex items-center gap-3">
                <CheckCircle className="h-5 w-5 text-yellow-500" />
                <div>
                  <p className="text-lg font-bold text-yellow-500">
                    {lists.reduce((sum, list) => sum + list.items.filter(item => item.completed).length, 0)}
                  </p>
                  <p className="text-xs text-muted-foreground">Completed</p>
                </div>
              </div>
            </div>

            <div className="vanta-card p-4 bg-gradient-to-br from-purple-500/10 to-pink-500/5 border-purple-500/20">
              <div className="flex items-center gap-3">
                <DollarSign className="h-5 w-5 text-purple-500" />
                <div>
                  <p className="text-lg font-bold text-purple-500">
                    ${lists.reduce((sum, list) => sum + (list.budget || 0), 0).toFixed(0)}
                  </p>
                  <p className="text-xs text-muted-foreground">Total Budget</p>
                </div>
              </div>
            </div>
          </div>

          {/* Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="lists" className="flex items-center gap-2">
                <ShoppingCart className="h-4 w-4" />
                Shopping Lists
              </TabsTrigger>
              <TabsTrigger value="categories" className="flex items-center gap-2">
                <Tag className="h-4 w-4" />
                Categories
              </TabsTrigger>
              <TabsTrigger value="analytics" className="flex items-center gap-2">
                <TrendingUp className="h-4 w-4" />
                Analytics
              </TabsTrigger>
            </TabsList>

            {/* Shopping Lists Tab */}
            <TabsContent value="lists" className="space-y-6">
              {/* Search and Filters */}
              <div className="vanta-card p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search items..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>

                  <Select value={filterCategory} onValueChange={setFilterCategory}>
                    <SelectTrigger>
                      <SelectValue placeholder="All categories" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Categories</SelectItem>
                      {categories.map(category => (
                        <SelectItem key={category.name} value={category.name}>
                          <span className="flex items-center gap-2">
                            <span>{category.icon}</span>
                            {category.name}
                          </span>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  <Select value={filterPriority} onValueChange={setFilterPriority}>
                    <SelectTrigger>
                      <SelectValue placeholder="All priorities" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Priorities</SelectItem>
                      <SelectItem value="high">High Priority</SelectItem>
                      <SelectItem value="medium">Medium Priority</SelectItem>
                      <SelectItem value="low">Low Priority</SelectItem>
                    </SelectContent>
                  </Select>

                  <div className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      id="show-completed"
                      checked={showCompleted}
                      onChange={(e) => setShowCompleted(e.target.checked)}
                      className="rounded"
                    />
                    <label htmlFor="show-completed" className="text-sm text-muted-foreground">
                      Show completed
                    </label>
                  </div>

                  <Button className="vanta-button">
                    <Filter className="h-4 w-4 mr-2" />
                    Clear Filters
                  </Button>
                </div>
              </div>

              {/* Add Item */}
              <div className="vanta-card p-6">
                <h3 className="font-semibold text-foreground mb-4">Add New Item</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Input
                    placeholder="What do you need to buy?"
                    value={newItem}
                    onChange={(e) => setNewItem(e.target.value)}
                    onKeyPress={(e) => e.key === "Enter" && addItem()}
                    className="md:col-span-2"
                  />

                  <div className="flex gap-2">
                    <Select value={selectedList} onValueChange={setSelectedList}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select list" />
                      </SelectTrigger>
                      <SelectContent>
                        {lists.map(list => (
                          <SelectItem key={list.id} value={list.id}>
                            <span className="flex items-center gap-2">
                              <div className={`w-3 h-3 rounded-full ${list.color}`}></div>
                              {list.name}
                            </span>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>

                    <Button onClick={addItem} className="vanta-button">
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>

              {/* Enhanced Shopping Lists */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {lists.filter(list => !list.archived).map(list => {
                  const stats = getListStats(list);
                  const filteredItems = getFilteredItems(list.items);

                  return (
                    <div key={list.id} className="vanta-card p-6 hover:shadow-lg transition-all duration-200">
                      {/* List Header */}
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center gap-3">
                          <div className={`w-4 h-4 rounded-full ${list.color}`}></div>
                          <div>
                            <h3 className="font-semibold text-foreground">{list.name}</h3>
                            {list.description && (
                              <p className="text-xs text-muted-foreground">{list.description}</p>
                            )}
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          {list.shared && (
                            <Badge variant="outline" className="text-xs">
                              <Share className="h-3 w-3 mr-1" />
                              Shared
                            </Badge>
                          )}
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button size="sm" variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem>
                                <Edit className="h-4 w-4 mr-2" />
                                Edit List
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Copy className="h-4 w-4 mr-2" />
                                Duplicate
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Share className="h-4 w-4 mr-2" />
                                Share
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Archive className="h-4 w-4 mr-2" />
                                Archive
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </div>

                      {/* List Stats */}
                      <div className="grid grid-cols-2 gap-4 mb-4">
                        <div className="text-center p-3 bg-white/5 rounded-lg">
                          <p className="text-lg font-bold text-foreground">{stats.completedItems}/{stats.totalItems}</p>
                          <p className="text-xs text-muted-foreground">Items Done</p>
                        </div>
                        <div className="text-center p-3 bg-white/5 rounded-lg">
                          <p className="text-lg font-bold text-foreground">R{stats.totalEstimated.toFixed(0)}</p>
                          <p className="text-xs text-muted-foreground">Estimated Cost</p>
                        </div>
                      </div>

                      {/* Progress Bar */}
                      <div className="mb-4">
                        <div className="flex justify-between text-xs text-muted-foreground mb-1">
                          <span>Progress</span>
                          <span>{Math.round(stats.progress)}%</span>
                        </div>
                        <Progress value={stats.progress} className="h-2" />
                      </div>

                      {/* Store and Budget Info */}
                      {(list.store || list.budget) && (
                        <div className="flex items-center justify-between mb-4 text-sm">
                          {list.store && (
                            <div className="flex items-center gap-1 text-muted-foreground">
                              <MapPin className="h-3 w-3" />
                              <span>{list.store}</span>
                            </div>
                          )}
                          {list.budget && (
                            <div className="flex items-center gap-1 text-muted-foreground">
                              <DollarSign className="h-3 w-3" />
                              <span>Budget: ${list.budget}</span>
                              {stats.remainingBudget < 0 && (
                                <Badge variant="destructive" className="text-xs ml-1">
                                  Over budget
                                </Badge>
                              )}
                            </div>
                          )}
                        </div>
                      )}

                      {/* Items List */}
                      <div className="space-y-2 max-h-64 overflow-y-auto vanta-scrollbar">
                        {filteredItems.map(item => (
                          <div
                            key={item.id}
                            className="group p-3 rounded-lg bg-white/5 hover:bg-white/10 transition-all duration-200"
                          >
                            <div className="flex items-start gap-3">
                              <Checkbox
                                checked={item.completed}
                                onCheckedChange={() => toggleItem(list.id, item.id)}
                                className="mt-0.5"
                              />

                              <div className="flex-1 min-w-0">
                                <div className="flex items-center gap-2 mb-1">
                                  <span
                                    className={`text-sm font-medium truncate ${
                                      item.completed
                                        ? "line-through text-muted-foreground"
                                        : "text-foreground"
                                    }`}
                                  >
                                    {item.name}
                                  </span>
                                  <Badge variant="outline" className={`text-xs ${getPriorityColor(item.priority)}`}>
                                    {item.priority}
                                  </Badge>
                                </div>

                                <div className="flex items-center gap-4 text-xs text-muted-foreground">
                                  <span className="flex items-center gap-1">
                                    <Package className="h-3 w-3" />
                                    {item.quantity} {item.unit || 'pcs'}
                                  </span>

                                  {item.estimatedPrice && (
                                    <span className="flex items-center gap-1">
                                      <DollarSign className="h-3 w-3" />
                                      R{item.estimatedPrice.toFixed(2)}
                                    </span>
                                  )}

                                  <span className="flex items-center gap-1">
                                    <Tag className="h-3 w-3" />
                                    {item.category}
                                  </span>
                                </div>

                                {item.brand && (
                                  <p className="text-xs text-muted-foreground mt-1">
                                    Brand: {item.brand}
                                  </p>
                                )}

                                {item.notes && (
                                  <p className="text-xs text-muted-foreground italic mt-1">
                                    "{item.notes}"
                                  </p>
                                )}
                              </div>

                              <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  className="h-6 w-6 p-0"
                                >
                                  <Edit className="h-3 w-3" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => deleteItem(list.id, item.id)}
                                  className="h-6 w-6 p-0 text-destructive hover:text-destructive"
                                >
                                  <Trash2 className="h-3 w-3" />
                                </Button>
                              </div>
                            </div>
                          </div>
                        ))}

                        {filteredItems.length === 0 && (
                          <div className="text-center py-8">
                            <ShoppingCart className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                            <p className="text-sm text-muted-foreground">
                              {list.items.length === 0 ? "No items in this list" : "No items match your filters"}
                            </p>
                          </div>
                        )}
                      </div>

                      {/* Last Modified */}
                      <div className="mt-4 pt-4 border-t border-border/30 text-xs text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          <span>Last modified {format(list.lastModified, "MMM d, HH:mm")}</span>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </TabsContent>

            {/* Categories Tab */}
            <TabsContent value="categories" className="space-y-6">
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                {categories.map(category => {
                  const categoryItems = lists.flatMap(list => list.items).filter(item => item.category === category.name);
                  const completedItems = categoryItems.filter(item => item.completed).length;
                  const totalItems = categoryItems.length;
                  const progress = totalItems > 0 ? (completedItems / totalItems) * 100 : 0;

                  return (
                    <div key={category.name} className="vanta-card p-4 text-center hover:shadow-lg transition-all duration-200">
                      <div className="text-3xl mb-2">{category.icon}</div>
                      <h3 className="font-medium text-foreground mb-1">{category.name}</h3>
                      <p className="text-xs text-muted-foreground mb-3">
                        {totalItems} item{totalItems !== 1 ? 's' : ''}
                      </p>

                      {totalItems > 0 && (
                        <>
                          <div className="w-full bg-muted rounded-full h-2 mb-2">
                            <div
                              className={`h-2 rounded-full transition-all duration-300 ${category.color.replace('text-', 'bg-')}`}
                              style={{ width: `${progress}%` }}
                            />
                          </div>
                          <p className="text-xs text-muted-foreground">
                            {Math.round(progress)}% complete
                          </p>
                        </>
                      )}
                    </div>
                  );
                })}
              </div>

              {/* Category Insights */}
              <div className="vanta-card p-6">
                <h3 className="font-semibold text-foreground mb-4">Category Insights</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-medium mb-3">Most Shopped Categories</h4>
                    <div className="space-y-2">
                      {categories
                        .map(category => ({
                          ...category,
                          count: lists.flatMap(list => list.items).filter(item => item.category === category.name).length
                        }))
                        .sort((a, b) => b.count - a.count)
                        .slice(0, 5)
                        .map(category => (
                          <div key={category.name} className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <span>{category.icon}</span>
                              <span className="text-sm">{category.name}</span>
                            </div>
                            <Badge variant="outline" className="text-xs">
                              {category.count} items
                            </Badge>
                          </div>
                        ))}
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium mb-3">Spending by Category</h4>
                    <div className="space-y-2">
                      {categories
                        .map(category => ({
                          ...category,
                          spending: lists.flatMap(list => list.items)
                            .filter(item => item.category === category.name)
                            .reduce((sum, item) => sum + (item.estimatedPrice || 0), 0)
                        }))
                        .filter(category => category.spending > 0)
                        .sort((a, b) => b.spending - a.spending)
                        .slice(0, 5)
                        .map(category => (
                          <div key={category.name} className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <span>{category.icon}</span>
                              <span className="text-sm">{category.name}</span>
                            </div>
                            <Badge variant="outline" className="text-xs">
                              ${category.spending.toFixed(2)}
                            </Badge>
                          </div>
                        ))}
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            {/* Analytics Tab */}
            <TabsContent value="analytics" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="vanta-card p-6">
                  <h3 className="font-semibold text-foreground mb-4 flex items-center gap-2">
                    <TrendingUp className="h-5 w-5 text-primary" />
                    Shopping Statistics
                  </h3>

                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Total Lists</span>
                      <span className="font-medium">{lists.length}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Total Items</span>
                      <span className="font-medium">{lists.reduce((sum, list) => sum + list.items.length, 0)}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Completed Items</span>
                      <span className="font-medium">{lists.reduce((sum, list) => sum + list.items.filter(item => item.completed).length, 0)}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Total Budget</span>
                      <span className="font-medium">R{lists.reduce((sum, list) => sum + (list.budget || 0), 0).toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Estimated Spending</span>
                      <span className="font-medium">
                        R{lists.reduce((sum, list) => sum + list.items.reduce((itemSum, item) => itemSum + (item.estimatedPrice || 0), 0), 0).toFixed(2)}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="vanta-card p-6">
                  <h3 className="font-semibold text-foreground mb-4">Recent Activity</h3>
                  <div className="space-y-3">
                    {lists
                      .flatMap(list => list.items.map(item => ({ ...item, listName: list.name, listColor: list.color })))
                      .sort((a, b) => b.addedDate.getTime() - a.addedDate.getTime())
                      .slice(0, 5)
                      .map(item => (
                        <div key={item.id} className="flex items-center gap-3">
                          <div className={`w-2 h-2 rounded-full ${item.listColor}`}></div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium truncate">{item.name}</p>
                            <p className="text-xs text-muted-foreground">
                              Added to {item.listName} • {format(item.addedDate, "MMM d")}
                            </p>
                          </div>
                          {item.completed && (
                            <CheckCircle className="h-4 w-4 text-green-500" />
                          )}
                        </div>
                      ))}
                  </div>
                </div>
              </div>

              <div className="vanta-card p-6">
                <h3 className="font-semibold text-foreground mb-4">Shopping Habits</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="text-center">
                    <div className="w-16 h-16 mx-auto mb-3 rounded-full bg-blue-500/20 flex items-center justify-center">
                      <Calendar className="h-8 w-8 text-blue-500" />
                    </div>
                    <p className="text-lg font-bold text-blue-500">
                      {Math.round(lists.reduce((sum, list) => sum + list.items.length, 0) / lists.length)}
                    </p>
                    <p className="text-sm text-muted-foreground">Avg Items per List</p>
                  </div>

                  <div className="text-center">
                    <div className="w-16 h-16 mx-auto mb-3 rounded-full bg-green-500/20 flex items-center justify-center">
                      <DollarSign className="h-8 w-8 text-green-500" />
                    </div>
                    <p className="text-lg font-bold text-green-500">
                      R{(lists.reduce((sum, list) => sum + list.items.reduce((itemSum, item) => itemSum + (item.estimatedPrice || 0), 0), 0) / lists.length).toFixed(2)}
                    </p>
                    <p className="text-sm text-muted-foreground">Avg Cost per List</p>
                  </div>

                  <div className="text-center">
                    <div className="w-16 h-16 mx-auto mb-3 rounded-full bg-purple-500/20 flex items-center justify-center">
                      <Star className="h-8 w-8 text-purple-500" />
                    </div>
                    <p className="text-lg font-bold text-purple-500">
                      {Math.round((lists.reduce((sum, list) => sum + list.items.filter(item => item.completed).length, 0) / lists.reduce((sum, list) => sum + list.items.length, 0)) * 100)}%
                    </p>
                    <p className="text-sm text-muted-foreground">Completion Rate</p>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </Layout>
    </AuthGuard>
  );
}