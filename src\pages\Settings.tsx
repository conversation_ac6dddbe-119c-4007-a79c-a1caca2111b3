import { useState, useEffect } from "react";
import { Settings as SettingsIcon, MapPin, DollarSign, User, Bell, Shield, Moon, Globe, Loader2 } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { locationService } from "@/services/locationService";
import { currencyService } from "@/utils/currency";
import { useToast } from "@/hooks/use-toast";
import Layout from "@/components/Layout";
import AuthGuard from "@/components/AuthGuard";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";

export default function Settings() {
  const { toast } = useToast();
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [detectingLocation, setDetectingLocation] = useState(false);

  const [settings, setSettings] = useState({
    location: "Johannesburg, South Africa",
    currency: "ZAR",
    timezone: "Africa/Johannesburg",
    notifications: true,
    biometric: false,
    darkMode: true,
    language: "en"
  });

  useEffect(() => {
    // Get current user
    const getCurrentUser = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      setUser(user);
    };

    getCurrentUser();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      setUser(session?.user ?? null);
    });

    return () => subscription.unsubscribe();
  }, []);

  useEffect(() => {
    if (user?.id) {
      loadUserSettings();
    }
  }, [user?.id]);

  const loadUserSettings = async () => {
    try {
      setLoading(true);

      // Load user profile from Supabase
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user!.id)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        throw error;
      }

      if (profile) {
        setSettings(prev => ({
          ...prev,
          timezone: profile.timezone || prev.timezone,
          biometric: profile.biometric_enabled || prev.biometric,
          darkMode: profile.theme_preference === 'dark'
        }));
      }

      // Load currency from localStorage/service
      const currentCurrency = currencyService.getCurrentCurrency();
      setSettings(prev => ({ ...prev, currency: currentCurrency }));

    } catch (error: any) {
      console.error('Error loading settings:', error);
      toast({
        title: "Error",
        description: "Failed to load settings. Using defaults.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const updateSetting = async (key: string, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }));

    // Save to appropriate service/storage
    try {
      if (key === 'currency') {
        currencyService.setCurrency(value);
      }

      // Save profile settings to Supabase
      if (['timezone', 'biometric', 'darkMode'].includes(key)) {
        await saveProfileSettings({ [key]: value });
      }
    } catch (error: any) {
      console.error('Error updating setting:', error);
      toast({
        title: "Error",
        description: "Failed to save setting. Please try again.",
        variant: "destructive",
      });
    }
  };

  const saveProfileSettings = async (updates: Record<string, any>) => {
    try {
      setSaving(true);

      const profileUpdates: any = {};

      if ('timezone' in updates) {
        profileUpdates.timezone = updates.timezone;
      }
      if ('biometric' in updates) {
        profileUpdates.biometric_enabled = updates.biometric;
      }
      if ('darkMode' in updates) {
        profileUpdates.theme_preference = updates.darkMode ? 'dark' : 'light';
      }

      const { error } = await supabase
        .from('profiles')
        .upsert({
          id: user!.id,
          email: user!.email!,
          ...profileUpdates,
          updated_at: new Date().toISOString()
        });

      if (error) throw error;

      toast({
        title: "Success",
        description: "Settings saved successfully.",
      });
    } catch (error: any) {
      console.error('Error saving profile settings:', error);
      throw error;
    } finally {
      setSaving(false);
    }
  };

  const detectCurrentLocation = async () => {
    try {
      setDetectingLocation(true);

      const location = await locationService.getCurrentLocation();
      const locationString = locationService.getLocationString(location);

      setSettings(prev => ({
        ...prev,
        location: locationString,
        timezone: location.timezone
      }));

      // Save timezone to profile
      await saveProfileSettings({ timezone: location.timezone });

      toast({
        title: "Location Updated",
        description: `Location set to ${locationString}`,
      });
    } catch (error: any) {
      console.error('Error detecting location:', error);
      toast({
        title: "Location Error",
        description: "Failed to detect location. Please enter manually.",
        variant: "destructive",
      });
    } finally {
      setDetectingLocation(false);
    }
  };

  return (
    <AuthGuard>
      <Layout>
        <div className="space-y-6 vanta-fade-in max-w-4xl mx-auto">
          {/* Header */}
          <div>
            <h1 className="text-3xl font-semibold text-foreground vanta-title">Settings</h1>
            <p className="text-muted-foreground vanta-subtitle mt-1">
              Customize your VANTA experience
            </p>
          </div>

          {loading && (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          )}

          {!loading && (
            <>

          {/* Profile Settings */}
          <Card className="vanta-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Profile
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground mb-2 block">
                  Full Name
                </label>
                <Input
                  placeholder="Your full name"
                  className="vanta-input"
                />
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground mb-2 block">
                  Email
                </label>
                <Input
                  placeholder="<EMAIL>"
                  type="email"
                  className="vanta-input"
                />
              </div>
            </CardContent>
          </Card>

          {/* Location & Currency */}
          <Card className="vanta-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                Location & Currency
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground mb-2 block">
                  Location
                </label>
                <div className="flex gap-2">
                  <Input
                    value={settings.location}
                    onChange={(e) => updateSetting('location', e.target.value)}
                    className="vanta-input flex-1"
                    placeholder="Enter your location"
                  />
                  <Button
                    variant="outline"
                    onClick={detectCurrentLocation}
                    disabled={detectingLocation}
                    className="px-3"
                  >
                    {detectingLocation ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <MapPin className="h-4 w-4" />
                    )}
                  </Button>
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  Click the location icon to auto-detect your current location
                </p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-muted-foreground mb-2 block">
                  Currency
                </label>
                <Select value={settings.currency} onValueChange={(value) => updateSetting('currency', value)}>
                  <SelectTrigger className="vanta-input">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ZAR">South African Rand (R)</SelectItem>
                    <SelectItem value="USD">US Dollar ($)</SelectItem>
                    <SelectItem value="EUR">Euro (€)</SelectItem>
                    <SelectItem value="GBP">British Pound (£)</SelectItem>
                    <SelectItem value="JPY">Japanese Yen (¥)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium text-muted-foreground mb-2 block">
                  Timezone
                </label>
                <Select value={settings.timezone} onValueChange={(value) => updateSetting('timezone', value)}>
                  <SelectTrigger className="vanta-input">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Africa/Johannesburg">Africa/Johannesburg</SelectItem>
                    <SelectItem value="America/New_York">America/New_York</SelectItem>
                    <SelectItem value="Europe/London">Europe/London</SelectItem>
                    <SelectItem value="Asia/Tokyo">Asia/Tokyo</SelectItem>
                    <SelectItem value="Australia/Sydney">Australia/Sydney</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Preferences */}
          <Card className="vanta-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <SettingsIcon className="h-5 w-5" />
                Preferences
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-foreground">Push Notifications</p>
                  <p className="text-sm text-muted-foreground">Receive notifications for reminders and updates</p>
                </div>
                <Switch
                  checked={settings.notifications}
                  onCheckedChange={(checked) => updateSetting('notifications', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-foreground">Biometric Authentication</p>
                  <p className="text-sm text-muted-foreground">Use fingerprint or face recognition to unlock</p>
                </div>
                <Switch
                  checked={settings.biometric}
                  onCheckedChange={(checked) => updateSetting('biometric', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-foreground">Dark Mode</p>
                  <p className="text-sm text-muted-foreground">Use dark theme throughout the app</p>
                </div>
                <Switch
                  checked={settings.darkMode}
                  onCheckedChange={(checked) => updateSetting('darkMode', checked)}
                />
              </div>
            </CardContent>
          </Card>

          {/* Security */}
          <Card className="vanta-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Security
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button variant="outline" className="w-full justify-start">
                Change Password
              </Button>
              <Button variant="outline" className="w-full justify-start">
                Two-Factor Authentication
              </Button>
              <Button variant="outline" className="w-full justify-start">
                Export Data
              </Button>
              <Button variant="destructive" className="w-full justify-start">
                Delete Account
              </Button>
            </CardContent>
          </Card>

          {/* Save Settings */}
          <div className="flex gap-3">
            <Button
              className="flex-1 vanta-button"
              disabled={saving}
              onClick={() => saveProfileSettings({})}
            >
              {saving ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Saving...
                </>
              ) : (
                'Save Changes'
              )}
            </Button>
            <Button variant="outline" className="flex-1">
              Reset to Defaults
            </Button>
          </div>
          </>
          )}
        </div>
      </Layout>
    </AuthGuard>
  );
}
