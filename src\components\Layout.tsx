import { useState } from "react";
import {
  Calendar,
  CheckSquare,
  DollarSign,
  Heart,
  Home,
  MessageSquare,
  ShoppingCart,
  Star,
  StickyNote,
  LogOut,
  Timer
} from "lucide-react";
import { NavLink, useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

interface LayoutProps {
  children: React.ReactNode;
}

const navigation = [
  { name: "Dashboard", href: "/", icon: Home },
  { name: "Calendar", href: "/calendar", icon: Calendar },
  { name: "Tasks", href: "/tasks", icon: CheckSquare },
  { name: "Finance", href: "/finance", icon: DollarSign },
  { name: "Wishlist", href: "/wishlist", icon: Star },
  { name: "Notes", href: "/notes", icon: StickyNote },
];

export default function Layout({ children }: LayoutProps) {
  const [isNavOpen, setIsNavOpen] = useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();

  const handleLogout = async () => {
    const { error } = await supabase.auth.signOut();
    if (error) {
      toast({
        title: "Error",
        description: "Failed to log out",
        variant: "destructive",
      });
    } else {
      toast({
        title: "Logged out",
        description: "You've been successfully logged out.",
      });
      navigate("/auth");
    }
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Enhanced Mobile Navigation */}
      <div className="vanta-mobile-nav">
        <nav className="vanta-glass border-t border-border/50 backdrop-blur-xl vanta-safe-area">
          <div className="flex justify-around items-center py-2 px-2 vanta-mobile-padding">
            {navigation.slice(0, 5).map((item) => (
              <NavLink
                key={item.name}
                to={item.href}
                className={({ isActive }) =>
                  `vanta-touch-target flex flex-col items-center p-2 rounded-xl transition-all duration-300 vanta-focus ${
                    isActive
                      ? "text-primary bg-primary/15 scale-105"
                      : "text-muted-foreground hover:text-foreground hover:bg-white/5"
                  }`
                }
              >
                <item.icon className="h-4 w-4" />
                <span className="text-xs mt-1 font-medium truncate">{item.name}</span>
              </NavLink>
            ))}
          </div>
        </nav>
      </div>

      {/* Desktop Sidebar */}
      <div className="hidden md:flex">
        <div className="fixed inset-y-0 left-0 w-64 vanta-glass border-r border-border/50 backdrop-blur-xl">
          <div className="flex flex-col h-full">
            {/* Clean Logo */}
            <div className="flex flex-col items-center justify-center h-20 border-b border-border/30 px-4">
              <img
                src="/assets/VANTA Logo.png"
                alt="VANTA"
                className="w-48 h-auto object-contain"
              />
              <p className="text-xs text-muted-foreground mt-1 font-medium">by XSHLabs</p>
            </div>

            {/* Navigation */}
            <nav className="flex-1 px-4 py-6 space-y-2">
              {navigation.map((item) => (
                <NavLink
                  key={item.name}
                  to={item.href}
                  className={({ isActive }) =>
                    `flex items-center px-4 py-3 rounded-xl text-sm font-medium transition-all duration-300 group ${
                      isActive
                        ? "bg-primary/15 text-primary border border-primary/30 shadow-lg"
                        : "text-muted-foreground hover:text-foreground hover:bg-white/5 hover:border-primary/20"
                    }`
                  }
                >
                  <item.icon className="h-5 w-5 mr-3 transition-transform group-hover:scale-110" />
                  {item.name}
                </NavLink>
              ))}
            </nav>

            {/* Logout Button */}
            <div className="px-4 py-4 border-t border-border/50">
              <Button
                variant="outline"
                onClick={handleLogout}
                className="w-full gap-2 vanta-button hover:bg-destructive/10 hover:text-destructive hover:border-destructive/30"
              >
                <LogOut className="h-4 w-4" />
                Log Out
              </Button>
            </div>
          </div>
        </div>

        {/* Enhanced Main Content */}
        <div className="ml-64 flex-1">
          <main className="p-6 vanta-safe-area">
            <div className="max-w-7xl mx-auto">
              {children}
            </div>
          </main>
        </div>
      </div>

      {/* Enhanced Mobile Content */}
      <div className="md:hidden pb-20 vanta-safe-area">
        <main className="vanta-mobile-padding py-4">
          <div className="max-w-full mx-auto">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}