// Simple script to set up Supabase database tables
// Run this with: node setup_database.js

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://qtlzonnhzyvoumbyyfci.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF0bHpvbm5oenl2b3VtYnl5ZmNpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQ0ODczNjgsImV4cCI6MjA3MDA2MzM2OH0.NAQkB0E7It0WlrSy6o0ONadjbHj5mXGA_19hHFCcNHc';

const supabase = createClient(supabaseUrl, supabaseKey);

async function setupDatabase() {
  console.log('Setting up database tables...');
  
  try {
    // Test connection
    const { data, error } = await supabase.from('profiles').select('count').limit(1);
    
    if (error && error.code === 'PGRST116') {
      console.log('Tables need to be created. Please run the SQL script in Supabase dashboard.');
      console.log('Go to: https://supabase.com/dashboard/project/qtlzonnhzyvoumbyyfci/sql');
      console.log('Copy and paste the contents of database_setup.sql');
    } else if (error) {
      console.error('Database connection error:', error);
    } else {
      console.log('Database tables already exist and are accessible.');
    }
  } catch (error) {
    console.error('Setup failed:', error);
  }
}

setupDatabase();
