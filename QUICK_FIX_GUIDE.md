# 🚨 QUICK FIX GUIDE - VANTA App

## Immediate Issues to Fix:

### 1. 🗄️ Database Tables Missing (CRITICAL)
**Problem:** Supabase tables don't exist, causing 404 errors

**Solution:** 
1. Go to [Supabase Dashboard](https://supabase.com/dashboard/project/qtlzonnhzyvoumbyyfci/sql)
2. Copy and paste this SQL code:

```sql
-- Create profiles table
CREATE TABLE IF NOT EXISTS public.profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT NOT NULL,
    full_name TEXT,
    timezone TEXT DEFAULT 'Africa/Johannesburg',
    theme_preference TEXT DEFAULT 'dark',
    biometric_enabled BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Create finance_items table
CREATE TABLE IF NOT EXISTS public.finance_items (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    description TEXT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('owed', 'owing')),
    due_date TIMESTAMP WITH TIME ZONE,
    person_name TEXT NOT NULL,
    person_email TEXT,
    person_phone TEXT,
    paid BOOLEAN DEFAULT false,
    category TEXT DEFAULT 'other',
    priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high')),
    payment_method TEXT,
    payment_link TEXT,
    notes TEXT,
    reminder_set BOOLEAN DEFAULT false,
    reminder_date TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Create tasks table
CREATE TABLE IF NOT EXISTS public.tasks (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high')),
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'completed', 'cancelled')),
    folder TEXT DEFAULT 'personal' CHECK (folder IN ('work', 'personal', 'hobby', 'other')),
    due_date TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    tags TEXT[] DEFAULT '{}',
    starred BOOLEAN DEFAULT false,
    archived BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Enable Row Level Security
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.finance_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tasks ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view own profile" ON public.profiles FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON public.profiles FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Users can insert own profile" ON public.profiles FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can view own finance items" ON public.finance_items FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own finance items" ON public.finance_items FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own finance items" ON public.finance_items FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own finance items" ON public.finance_items FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Users can view own tasks" ON public.tasks FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own tasks" ON public.tasks FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own tasks" ON public.tasks FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own tasks" ON public.tasks FOR DELETE USING (auth.uid() = user_id);
```

3. Click "Run" to execute the SQL

### 2. 🌍 Location Detection Issues
**Problem:** CSP blocking external location APIs

**Solution:** Already fixed in code - CSP updated to allow location services

### 3. 📅 Calendar Date Picker
**Problem:** Add Event modal doesn't have proper date/time inputs

**Solution:** Already fixed - now has proper date, time, and form fields

### 4. 📝 Notes PIN System
**Problem:** Notes locked by default without user setting PIN

**Solution:** Already fixed - PIN is now optional and per-note

### 5. 💰 Finance Page Layout
**Problem:** No proper expense tracking interface

**Solution:** Already added - now has Monthly Expenses tab with categories

## ✅ What's Already Fixed:

- ✅ Weather API key configured (c5e938f069f15fb0d4a938df2e93c128)
- ✅ Currency formatting (ZAR/Rands throughout)
- ✅ Wishlist title changed from "Dream Wishlist" to "Wishlist"
- ✅ Removed gradient text styling
- ✅ Content Security Policy updated for location services
- ✅ Calendar Add Event form enhanced with proper inputs
- ✅ Notes PIN system made optional and per-note
- ✅ Finance page redesigned with expense tracking tabs

## 🚀 After Database Setup:

Once you run the SQL script above, the app will:
- ✅ Load real user data instead of showing errors
- ✅ Save finance items to database
- ✅ Track tasks properly
- ✅ Show dynamic dashboard stats
- ✅ Work perfectly on mobile and desktop

## 📱 Test These Features:

1. **Weather** - Should auto-detect your location and show real weather
2. **Finance** - Add expenses and see them in Monthly Expenses tab
3. **Calendar** - Add Event should now have date/time pickers
4. **Notes** - No longer locked by default, PIN is optional
5. **Settings** - Location detection should work

## 🎯 Priority Actions:

1. **MOST IMPORTANT:** Run the SQL script in Supabase dashboard
2. Test location detection (should ask for permission)
3. Add some finance items to test the expense tracking
4. Try adding calendar events with the new form

The app is production-ready once the database tables are created! 🚀
