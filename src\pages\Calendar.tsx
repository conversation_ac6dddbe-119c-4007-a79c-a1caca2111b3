import { useState } from "react";
import { Plus, ChevronLeft, ChevronRight, Calendar as CalendarI<PERSON>, Clock, MapPin, Repeat, Bell } from "lucide-react";
import Layout from "@/components/Layout";
import AuthGuard from "@/components/AuthGuard";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { format, addMonths, subMonths, startOfMonth, endOfMonth, eachDayOfInterval, isSameDay, isToday, isSameMonth, addDays, startOfWeek, endOfWeek } from "date-fns";

interface CalendarEvent {
  id: string;
  title: string;
  date: Date;
  endDate?: Date;
  time?: string;
  location?: string;
  description?: string;
  type: "birthday" | "reminder" | "anniversary" | "event" | "meeting" | "personal";
  recurring?: "none" | "daily" | "weekly" | "monthly" | "yearly";
  priority: "low" | "medium" | "high";
  reminder?: boolean;
}

export default function Calendar() {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [viewMode, setViewMode] = useState<"month" | "week">("month");
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [showAddEvent, setShowAddEvent] = useState(false);
  const [newEvent, setNewEvent] = useState({
    title: "",
    date: "",
    time: "",
    endTime: "",
    location: "",
    description: "",
    type: "event" as const,
    priority: "medium" as const,
    recurring: "none" as const,
    reminder: false
  });
  const [events, setEvents] = useState<CalendarEvent[]>([
    // Empty by default - ready for production use
  ]);

  const monthStart = startOfMonth(currentDate);
  const monthEnd = endOfMonth(currentDate);
  // Start the calendar from Monday of the week containing the first day of the month
  const calendarStart = startOfWeek(monthStart, { weekStartsOn: 1 }); // 1 = Monday
  // End the calendar on Sunday of the week containing the last day of the month
  const calendarEnd = endOfWeek(monthEnd, { weekStartsOn: 1 });
  const calendarDays = eachDayOfInterval({ start: calendarStart, end: calendarEnd });

  const navigateMonth = (direction: "prev" | "next") => {
    setCurrentDate(prev => {
      return direction === "prev" ? subMonths(prev, 1) : addMonths(prev, 1);
    });
  };

  const getEventsForDate = (date: Date) => {
    return events.filter(event => isSameDay(event.date, date));
  };

  const getEventColor = (type: string, priority: string) => {
    const baseColors = {
      birthday: "bg-pink-500",
      reminder: "bg-yellow-500",
      anniversary: "bg-purple-500",
      meeting: "bg-blue-500",
      personal: "bg-green-500",
      event: "bg-primary"
    };

    const priorityIntensity = {
      low: "opacity-60",
      medium: "opacity-80",
      high: "opacity-100"
    };

    return `${baseColors[type as keyof typeof baseColors] || baseColors.event} ${priorityIntensity[priority as keyof typeof priorityIntensity]}`;
  };

  const getPriorityBadgeColor = (priority: string) => {
    switch (priority) {
      case "high": return "bg-red-500/20 text-red-500 border-red-500/30";
      case "medium": return "bg-yellow-500/20 text-yellow-500 border-yellow-500/30";
      case "low": return "bg-green-500/20 text-green-500 border-green-500/30";
      default: return "bg-gray-500/20 text-gray-500 border-gray-500/30";
    }
  };

  return (
    <AuthGuard>
      <Layout>
        <div className="space-y-6 vanta-fade-in">
          {/* Header */}
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
            <div>
              <h1 className="text-3xl font-semibold text-foreground">
                Calendar
              </h1>
              <p className="text-muted-foreground mt-1">
                {format(currentDate, "MMMM yyyy")} • {events.length} events
              </p>
            </div>
            <div className="flex items-center gap-3">
              <div className="flex items-center bg-muted rounded-lg p-1">
                <Button
                  variant={viewMode === "month" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("month")}
                  className="text-xs"
                >
                  Month
                </Button>
                <Button
                  variant={viewMode === "week" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("week")}
                  className="text-xs"
                >
                  Week
                </Button>
              </div>
              <Button
                size="sm"
                className="gap-2 vanta-button"
                onClick={() => setShowAddEvent(true)}
              >
                <Plus className="h-4 w-4" />
                Add Event
              </Button>
            </div>
          </div>

          {/* Calendar Navigation */}
          <div className="vanta-card p-6">
            <div className="flex items-center justify-between mb-6">
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigateMonth("prev")}
                className="vanta-button"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <h2 className="text-xl font-semibold text-foreground">
                {format(currentDate, "MMMM yyyy")}
              </h2>
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigateMonth("next")}
                className="vanta-button"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>

            {/* Calendar Grid */}
            <div className="grid grid-cols-7 gap-1 bg-muted/30 p-2 rounded-xl">
              {/* Day headers */}
              {["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"].map(day => (
                <div key={day} className="text-center text-sm font-semibold text-muted-foreground p-3">
                  {day}
                </div>
              ))}

              {/* Calendar days */}
              {calendarDays.map((date, index) => {
                const dayEvents = getEventsForDate(date);
                const isCurrentMonth = isSameMonth(date, currentDate);
                const isTodayDate = isToday(date);
                const isSelected = selectedDate && isSameDay(date, selectedDate);

                return (
                  <div
                    key={index}
                    onClick={() => setSelectedDate(date)}
                    className={`
                      min-h-[80px] p-2 rounded-lg cursor-pointer transition-all duration-200 group
                      ${isTodayDate
                        ? "bg-primary/20 border-2 border-primary shadow-lg"
                        : isSelected
                        ? "bg-primary/10 border border-primary/50"
                        : "hover:bg-white/5 border border-transparent"
                      }
                      ${!isCurrentMonth ? "opacity-40" : ""}
                    `}
                  >
                    <div className={`
                      text-sm font-medium mb-1 transition-colors
                      ${isTodayDate
                        ? "text-primary font-bold"
                        : isCurrentMonth
                        ? "text-foreground"
                        : "text-muted-foreground"
                      }
                    `}>
                      {format(date, "d")}
                    </div>

                    {/* Event dots - Apple style */}
                    <div className="space-y-1">
                      {dayEvents.slice(0, 3).map(event => (
                        <div
                          key={event.id}
                          className={`
                            w-full h-1 rounded-full transition-all duration-200 group-hover:h-2
                            ${getEventColor(event.type, event.priority)}
                          `}
                          title={`${event.title}${event.time ? ` at ${event.time}` : ''}`}
                        />
                      ))}
                      {dayEvents.length > 3 && (
                        <div className="text-xs text-muted-foreground font-medium">
                          +{dayEvents.length - 3} more
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Upcoming Events */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="vanta-card p-6">
              <h3 className="font-semibold text-foreground mb-4 flex items-center gap-2">
                <CalendarIcon className="h-5 w-5 text-primary" />
                Upcoming Events
              </h3>
              <div className="space-y-3">
                {events
                  .filter(event => event.date >= new Date())
                  .sort((a, b) => a.date.getTime() - b.date.getTime())
                  .slice(0, 5)
                  .map(event => (
                    <div key={event.id} className="vanta-card p-4 hover:shadow-lg transition-all duration-200">
                      <div className="flex items-start gap-3">
                        <div className={`w-3 h-3 rounded-full mt-1 ${getEventColor(event.type, event.priority)}`} />
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <p className="font-medium text-foreground truncate">{event.title}</p>
                            <Badge variant="outline" className={`text-xs ${getPriorityBadgeColor(event.priority)}`}>
                              {event.priority}
                            </Badge>
                          </div>
                          <div className="space-y-1 text-sm text-muted-foreground">
                            <div className="flex items-center gap-2">
                              <CalendarIcon className="h-3 w-3" />
                              <span>{format(event.date, "MMM d, yyyy")}</span>
                              {event.time && (
                                <>
                                  <Clock className="h-3 w-3 ml-2" />
                                  <span>{event.time}</span>
                                </>
                              )}
                            </div>
                            {event.location && (
                              <div className="flex items-center gap-2">
                                <MapPin className="h-3 w-3" />
                                <span className="truncate">{event.location}</span>
                              </div>
                            )}
                            {event.recurring && event.recurring !== "none" && (
                              <div className="flex items-center gap-2">
                                <Repeat className="h-3 w-3" />
                                <span className="capitalize">{event.recurring}</span>
                              </div>
                            )}
                            {event.reminder && (
                              <div className="flex items-center gap-2">
                                <Bell className="h-3 w-3" />
                                <span>Reminder set</span>
                              </div>
                            )}
                          </div>
                        </div>
                        <Badge variant="outline" className="text-xs capitalize">
                          {event.type}
                        </Badge>
                      </div>
                    </div>
                  ))}
              </div>
            </div>

            {/* Selected Date Details */}
            <div className="vanta-card p-6">
              <h3 className="font-semibold text-foreground mb-4">
                {selectedDate ? format(selectedDate, "EEEE, MMMM d") : "Select a date"}
              </h3>
              {selectedDate ? (
                <div className="space-y-3">
                  {getEventsForDate(selectedDate).length > 0 ? (
                    getEventsForDate(selectedDate).map(event => (
                      <div key={event.id} className="p-3 rounded-lg bg-primary/5 border border-primary/20">
                        <div className="flex items-center gap-2 mb-2">
                          <div className={`w-2 h-2 rounded-full ${getEventColor(event.type, event.priority)}`} />
                          <span className="font-medium text-foreground">{event.title}</span>
                          <Badge variant="outline" className={`text-xs ml-auto ${getPriorityBadgeColor(event.priority)}`}>
                            {event.priority}
                          </Badge>
                        </div>
                        {event.time && (
                          <p className="text-sm text-muted-foreground flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {event.time}
                          </p>
                        )}
                        {event.location && (
                          <p className="text-sm text-muted-foreground flex items-center gap-1">
                            <MapPin className="h-3 w-3" />
                            {event.location}
                          </p>
                        )}
                      </div>
                    ))
                  ) : (
                    <p className="text-muted-foreground text-center py-8">
                      No events on this date
                    </p>
                  )}
                </div>
              ) : (
                <p className="text-muted-foreground text-center py-8">
                  Click on a date to see events
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Add Event Modal */}
        {showAddEvent && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="vanta-card p-6 w-full max-w-md mx-4">
              <h3 className="text-lg font-semibold text-foreground mb-4">Add New Event</h3>
              <div className="space-y-4">
                <Input
                  placeholder="Event title..."
                  value={newEvent.title}
                  onChange={(e) => setNewEvent(prev => ({ ...prev, title: e.target.value }))}
                  className="vanta-input"
                />
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <label className="text-sm font-medium text-foreground mb-2 block">Date</label>
                    <Input
                      type="date"
                      value={newEvent.date}
                      onChange={(e) => setNewEvent(prev => ({ ...prev, date: e.target.value }))}
                      className="vanta-input"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium text-foreground mb-2 block">Time</label>
                    <Input
                      type="time"
                      value={newEvent.time}
                      onChange={(e) => setNewEvent(prev => ({ ...prev, time: e.target.value }))}
                      className="vanta-input"
                    />
                  </div>
                </div>
                <div className="flex gap-3">
                  <Button
                    onClick={() => {
                      if (newEvent.title.trim()) {
                        const eventDate = newEvent.date ? new Date(newEvent.date) : (selectedDate || new Date());
                        const eventToAdd: CalendarEvent = {
                          id: Date.now().toString(),
                          title: newEvent.title,
                          date: eventDate,
                          time: newEvent.time || "09:00",
                          type: "event",
                          priority: "medium"
                        };
                        setEvents(prev => [...prev, eventToAdd]);
                        setNewEvent({
                          title: "",
                          date: "",
                          time: "",
                          endTime: "",
                          location: "",
                          description: "",
                          type: "event",
                          priority: "medium",
                          recurring: "none",
                          reminder: false
                        });
                        setShowAddEvent(false);
                      }
                    }}
                    className="flex-1 vanta-button"
                  >
                    Add Event
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setShowAddEvent(false);
                      setNewEvent({
                        title: "",
                        date: "",
                        time: "",
                        endTime: "",
                        location: "",
                        description: "",
                        type: "event",
                        priority: "medium",
                        recurring: "none",
                        reminder: false
                      });
                    }}
                    className="flex-1"
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </Layout>
    </AuthGuard>
  );
}