import { useState } from "react";
import { Timer, Target, Search, TrendingUp, BookOpen, Activity, Shield } from "lucide-react";
import Layout from "@/components/Layout";
import AuthGuard from "@/components/AuthGuard";
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import PomodoroTimer from "@/components/PomodoroTimer";
import HabitTracker from "@/components/HabitTracker";

export default function Productivity() {
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("pomodoro");

  return (
    <AuthGuard>
      <Layout>
        <div className="space-y-6 vanta-fade-in">
          {/* Header */}
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-foreground to-orange-500 bg-clip-text text-transparent">
                Productivity Hub
              </h1>
              <p className="text-muted-foreground mt-1">
                Boost your focus and build lasting habits with powerful productivity tools
              </p>
            </div>
            <div className="flex items-center gap-3">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search productivity tools..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 w-64"
                />
              </div>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="vanta-card p-4 bg-gradient-to-br from-red-500/10 to-orange-500/5 border-red-500/20">
              <div className="flex items-center gap-3">
                <Timer className="h-5 w-5 text-red-500" />
                <div>
                  <p className="text-lg font-bold text-red-500">4</p>
                  <p className="text-xs text-muted-foreground">Pomodoros Today</p>
                </div>
              </div>
            </div>
            
            <div className="vanta-card p-4 bg-gradient-to-br from-green-500/10 to-emerald-500/5 border-green-500/20">
              <div className="flex items-center gap-3">
                <Target className="h-5 w-5 text-green-500" />
                <div>
                  <p className="text-lg font-bold text-green-500">7</p>
                  <p className="text-xs text-muted-foreground">Habits Completed</p>
                </div>
              </div>
            </div>
            
            <div className="vanta-card p-4 bg-gradient-to-br from-blue-500/10 to-cyan-500/5 border-blue-500/20">
              <div className="flex items-center gap-3">
                <BookOpen className="h-5 w-5 text-blue-500" />
                <div>
                  <p className="text-lg font-bold text-blue-500">2.5h</p>
                  <p className="text-xs text-muted-foreground">Focus Time</p>
                </div>
              </div>
            </div>
            
            <div className="vanta-card p-4 bg-gradient-to-br from-purple-500/10 to-pink-500/5 border-purple-500/20">
              <div className="flex items-center gap-3">
                <TrendingUp className="h-5 w-5 text-purple-500" />
                <div>
                  <p className="text-lg font-bold text-purple-500">85%</p>
                  <p className="text-xs text-muted-foreground">Weekly Goal</p>
                </div>
              </div>
            </div>
          </div>

          {/* Main Content Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="pomodoro" className="flex items-center gap-2">
                <Timer className="h-4 w-4" />
                Pomodoro
              </TabsTrigger>
              <TabsTrigger value="habits" className="flex items-center gap-2">
                <Target className="h-4 w-4" />
                Habits
              </TabsTrigger>
              <TabsTrigger value="breaker" className="flex items-center gap-2">
                <Shield className="h-4 w-4" />
                Habit Breaker
              </TabsTrigger>
              <TabsTrigger value="journal" className="flex items-center gap-2">
                <BookOpen className="h-4 w-4" />
                Journal
              </TabsTrigger>
              <TabsTrigger value="analytics" className="flex items-center gap-2">
                <Activity className="h-4 w-4" />
                Analytics
              </TabsTrigger>
            </TabsList>

            {/* Pomodoro Tab */}
            <TabsContent value="pomodoro" className="space-y-6">
              <PomodoroTimer />
            </TabsContent>

            {/* Habits Tab */}
            <TabsContent value="habits" className="space-y-6">
              <HabitTracker />
            </TabsContent>

            {/* Habit Breaker Tab */}
            <TabsContent value="breaker" className="space-y-6">
              <div className="vanta-card p-6">
                <h3 className="font-semibold text-foreground mb-4 flex items-center gap-2">
                  <Shield className="h-5 w-5 text-primary" />
                  Break Bad Habits
                </h3>
                <p className="text-muted-foreground mb-6">
                  Choose a habit you want to quit and get AI-powered support and motivation.
                </p>

                <div className="space-y-6">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground mb-2 block">
                      What habit do you want to break?
                    </label>
                    <select className="w-full p-3 bg-white/5 border border-border rounded-xl">
                      <option value="">Select a habit to quit</option>
                      <option value="smoking">Smoking</option>
                      <option value="drinking">Excessive Drinking</option>
                      <option value="social_media">Social Media Addiction</option>
                      <option value="junk_food">Junk Food</option>
                      <option value="procrastination">Procrastination</option>
                      <option value="nail_biting">Nail Biting</option>
                      <option value="negative_thinking">Negative Thinking</option>
                      <option value="oversleeping">Oversleeping</option>
                      <option value="overspending">Overspending</option>
                      <option value="other">Other</option>
                    </select>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center p-4 bg-white/5 rounded-lg">
                      <div className="text-2xl font-bold text-green-500 mb-1">0</div>
                      <div className="text-xs text-muted-foreground">Days Clean</div>
                    </div>
                    <div className="text-center p-4 bg-white/5 rounded-lg">
                      <div className="text-2xl font-bold text-blue-500 mb-1">R0</div>
                      <div className="text-xs text-muted-foreground">Money Saved</div>
                    </div>
                    <div className="text-center p-4 bg-white/5 rounded-lg">
                      <div className="text-2xl font-bold text-purple-500 mb-1">0%</div>
                      <div className="text-xs text-muted-foreground">Health Improved</div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h4 className="font-medium text-foreground">AI Motivation</h4>
                    <div className="p-4 bg-white/5 rounded-lg border-l-4 border-primary">
                      <p className="text-sm text-foreground">
                        Ready to start your journey? Every expert was once a beginner. Every pro was once an amateur. Every icon was once an unknown. The key is to start, and the best time to start is now.
                      </p>
                    </div>
                  </div>

                  <div className="flex gap-3">
                    <Button className="flex-1 vanta-button">
                      Start My Journey
                    </Button>
                    <Button variant="outline" className="vanta-button">
                      Get AI Support
                    </Button>
                  </div>
                </div>
              </div>
            </TabsContent>

            {/* Journal Tab */}
            <TabsContent value="journal" className="space-y-6">
              <div className="vanta-card p-6">
                <h3 className="font-semibold text-foreground mb-4 flex items-center gap-2">
                  <BookOpen className="h-5 w-5 text-primary" />
                  Daily Journal
                </h3>
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground mb-2 block">
                      How was your day? (1-10)
                    </label>
                    <div className="flex gap-2">
                      {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map(rating => (
                        <Button
                          key={rating}
                          variant="outline"
                          size="sm"
                          className="w-10 h-10 p-0"
                        >
                          {rating}
                        </Button>
                      ))}
                    </div>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium text-muted-foreground mb-2 block">
                      What are you grateful for today?
                    </label>
                    <textarea
                      className="w-full p-3 bg-white/5 border border-border rounded-lg resize-none"
                      rows={3}
                      placeholder="Write down three things you're grateful for..."
                    />
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium text-muted-foreground mb-2 block">
                      What did you learn today?
                    </label>
                    <textarea
                      className="w-full p-3 bg-white/5 border border-border rounded-lg resize-none"
                      rows={3}
                      placeholder="Reflect on something new you discovered..."
                    />
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium text-muted-foreground mb-2 block">
                      Tomorrow's priority
                    </label>
                    <Input
                      placeholder="What's the most important thing to focus on tomorrow?"
                      className="bg-white/5"
                    />
                  </div>
                  
                  <Button className="w-full vanta-button">
                    Save Journal Entry
                  </Button>
                </div>
              </div>
            </TabsContent>

            {/* Analytics Tab */}
            <TabsContent value="analytics" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="vanta-card p-6">
                  <h3 className="font-semibold text-foreground mb-4 flex items-center gap-2">
                    <Activity className="h-5 w-5 text-primary" />
                    Productivity Trends
                  </h3>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Focus Sessions This Week</span>
                      <span className="font-medium">28</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Average Session Length</span>
                      <span className="font-medium">24 min</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Habits Completion Rate</span>
                      <span className="font-medium">85%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Best Productivity Day</span>
                      <span className="font-medium">Tuesday</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Current Streak</span>
                      <span className="font-medium">12 days</span>
                    </div>
                  </div>
                </div>
                
                <div className="vanta-card p-6">
                  <h3 className="font-semibold text-foreground mb-4">Weekly Goals</h3>
                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Pomodoro Sessions</span>
                        <span>28/30</span>
                      </div>
                      <div className="w-full bg-muted rounded-full h-2">
                        <div className="bg-red-500 h-2 rounded-full" style={{ width: "93%" }} />
                      </div>
                    </div>
                    
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Daily Habits</span>
                        <span>6/7</span>
                      </div>
                      <div className="w-full bg-muted rounded-full h-2">
                        <div className="bg-green-500 h-2 rounded-full" style={{ width: "86%" }} />
                      </div>
                    </div>
                    
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Journal Entries</span>
                        <span>5/7</span>
                      </div>
                      <div className="w-full bg-muted rounded-full h-2">
                        <div className="bg-blue-500 h-2 rounded-full" style={{ width: "71%" }} />
                      </div>
                    </div>
                    
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Focus Time</span>
                        <span>12h/15h</span>
                      </div>
                      <div className="w-full bg-muted rounded-full h-2">
                        <div className="bg-purple-500 h-2 rounded-full" style={{ width: "80%" }} />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="vanta-card p-6">
                <h3 className="font-semibold text-foreground mb-4">Achievements</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center p-4 bg-white/5 rounded-lg">
                    <div className="text-2xl mb-2">🏆</div>
                    <p className="text-sm font-medium">Focus Master</p>
                    <p className="text-xs text-muted-foreground">100 Pomodoros</p>
                  </div>
                  
                  <div className="text-center p-4 bg-white/5 rounded-lg">
                    <div className="text-2xl mb-2">🔥</div>
                    <p className="text-sm font-medium">Streak Legend</p>
                    <p className="text-xs text-muted-foreground">30 Day Streak</p>
                  </div>
                  
                  <div className="text-center p-4 bg-white/5 rounded-lg">
                    <div className="text-2xl mb-2">📚</div>
                    <p className="text-sm font-medium">Reflective Writer</p>
                    <p className="text-xs text-muted-foreground">50 Journal Entries</p>
                  </div>
                  
                  <div className="text-center p-4 bg-white/5 rounded-lg">
                    <div className="text-2xl mb-2">⭐</div>
                    <p className="text-sm font-medium">Habit Builder</p>
                    <p className="text-xs text-muted-foreground">All Habits 7 Days</p>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </Layout>
    </AuthGuard>
  );
}
