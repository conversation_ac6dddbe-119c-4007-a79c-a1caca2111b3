// Service Worker registration and management

const isLocalhost = Boolean(
  window.location.hostname === 'localhost' ||
  window.location.hostname === '[::1]' ||
  window.location.hostname.match(
    /^127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}$/
  )
);

export function registerSW() {
  if ('serviceWorker' in navigator) {
    const publicUrl = new URL(import.meta.env.BASE_URL, window.location.href);
    if (publicUrl.origin !== window.location.origin) {
      return;
    }

    window.addEventListener('load', () => {
      const swUrl = `${import.meta.env.BASE_URL}sw.js`;

      if (isLocalhost) {
        checkValidServiceWorker(swUrl);
        navigator.serviceWorker.ready.then(() => {
          console.log('Vanta: Service worker is ready for offline use.');
        });
      } else {
        registerValidSW(swUrl);
      }
    });
  }
}

function registerValidSW(swUrl: string) {
  navigator.serviceWorker
    .register(swUrl)
    .then((registration) => {
      console.log('Vanta: Service worker registered successfully:', registration);
      
      registration.onupdatefound = () => {
        const installingWorker = registration.installing;
        if (installingWorker == null) {
          return;
        }
        
        installingWorker.onstatechange = () => {
          if (installingWorker.state === 'installed') {
            if (navigator.serviceWorker.controller) {
              console.log('Vanta: New content is available; please refresh.');
              showUpdateAvailableNotification();
            } else {
              console.log('Vanta: Content is cached for offline use.');
              showOfflineReadyNotification();
            }
          }
        };
      };
    })
    .catch((error) => {
      console.error('Vanta: Service worker registration failed:', error);
    });
}

function checkValidServiceWorker(swUrl: string) {
  fetch(swUrl, {
    headers: { 'Service-Worker': 'script' },
  })
    .then((response) => {
      const contentType = response.headers.get('content-type');
      if (
        response.status === 404 ||
        (contentType != null && contentType.indexOf('javascript') === -1)
      ) {
        navigator.serviceWorker.ready.then((registration) => {
          registration.unregister().then(() => {
            window.location.reload();
          });
        });
      } else {
        registerValidSW(swUrl);
      }
    })
    .catch(() => {
      console.log('Vanta: No internet connection found. App is running in offline mode.');
    });
}

function showUpdateAvailableNotification() {
  // You can integrate this with your toast system
  if (window.confirm('A new version of Vanta is available. Would you like to update?')) {
    window.location.reload();
  }
}

function showOfflineReadyNotification() {
  console.log('Vanta is now ready to work offline!');
  // You can show a toast notification here
}

export function unregisterSW() {
  if ('serviceWorker' in navigator) {
    navigator.serviceWorker.ready
      .then((registration) => {
        registration.unregister();
      })
      .catch((error) => {
        console.error('Vanta: Service worker unregistration failed:', error);
      });
  }
}

// PWA Install prompt handling
let deferredPrompt: any;

export function initializePWAInstall() {
  window.addEventListener('beforeinstallprompt', (e) => {
    console.log('Vanta: PWA install prompt available');
    e.preventDefault();
    deferredPrompt = e;
    showInstallButton();
  });

  window.addEventListener('appinstalled', () => {
    console.log('Vanta: PWA was installed');
    hideInstallButton();
    deferredPrompt = null;
  });
}

export function showInstallPrompt() {
  if (deferredPrompt) {
    deferredPrompt.prompt();
    deferredPrompt.userChoice.then((choiceResult: any) => {
      if (choiceResult.outcome === 'accepted') {
        console.log('Vanta: User accepted the PWA install prompt');
      } else {
        console.log('Vanta: User dismissed the PWA install prompt');
      }
      deferredPrompt = null;
    });
  }
}

function showInstallButton() {
  // You can implement UI to show install button
  console.log('Vanta: PWA can be installed');
}

function hideInstallButton() {
  // You can implement UI to hide install button
  console.log('Vanta: PWA install button hidden');
}

// Network status monitoring
export function initializeNetworkMonitoring() {
  function updateOnlineStatus() {
    const isOnline = navigator.onLine;
    console.log(`Vanta: Network status changed - ${isOnline ? 'online' : 'offline'}`);
    
    // You can dispatch custom events or update global state here
    window.dispatchEvent(new CustomEvent('networkstatuschange', {
      detail: { isOnline }
    }));
  }

  window.addEventListener('online', updateOnlineStatus);
  window.addEventListener('offline', updateOnlineStatus);
  
  // Initial status
  updateOnlineStatus();
}

// Background sync registration
export function registerBackgroundSync(tag: string) {
  if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
    navigator.serviceWorker.ready.then((registration) => {
      return registration.sync.register(tag);
    }).catch((error) => {
      console.error('Vanta: Background sync registration failed:', error);
    });
  }
}

// Push notification subscription
export async function subscribeToPushNotifications() {
  if ('serviceWorker' in navigator && 'PushManager' in window) {
    try {
      const registration = await navigator.serviceWorker.ready;
      const subscription = await registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: urlBase64ToUint8Array(
          // Replace with your VAPID public key
          'YOUR_VAPID_PUBLIC_KEY_HERE'
        )
      });
      
      console.log('Vanta: Push notification subscription successful:', subscription);
      return subscription;
    } catch (error) {
      console.error('Vanta: Push notification subscription failed:', error);
      return null;
    }
  }
  return null;
}

function urlBase64ToUint8Array(base64String: string) {
  const padding = '='.repeat((4 - base64String.length % 4) % 4);
  const base64 = (base64String + padding)
    .replace(/-/g, '+')
    .replace(/_/g, '/');

  const rawData = window.atob(base64);
  const outputArray = new Uint8Array(rawData.length);

  for (let i = 0; i < rawData.length; ++i) {
    outputArray[i] = rawData.charCodeAt(i);
  }
  return outputArray;
}

// Initialize all PWA features
export function initializePWA() {
  registerSW();
  initializePWAInstall();
  initializeNetworkMonitoring();
  
  console.log('Vanta: PWA features initialized');
}
