# Vanta - Minimalist Power Hub

> **Your minimalist life management powerhouse** - Developed by XSHLabs

Vanta is a comprehensive, Apple-inspired life management application that combines productivity, wellness, and organization into one seamless experience. Built with modern web technologies and designed for cross-platform mobile deployment.

## ✨ Features

### 🎯 Core Productivity
- **Smart Dashboard** - Personalized overview with weather, motivational quotes, and quick stats
- **Advanced Calendar** - Apple-style event management with recurring events and reminders
- **Intelligent Tasks** - Sub-tasks, priority tags, due dates, and smart categorization
- **Finance Tracker** - Comprehensive money management with payment links and reminders

### 🧠 Wellness & Growth
- **AI Therapist** - Empathetic chat companion with therapeutic exercises and mood tracking
- **Habit Tracker** - Visual habit building with streaks and progress analytics
- **Pomodoro Timer** - Focus sessions with customizable intervals and statistics
- **Daily Journaling** - Guided reflection prompts and gratitude practice

### 🛍️ Lifestyle Management
- **Smart Shopping** - Intelligent categorization, budget tracking, and shared lists
- **Dream Wishlist** - Save and track desired items with price monitoring
- **Quick Notes** - Capture thoughts with tags and categories
- **Affirmations** - Daily positive reinforcement and motivation

### 🔒 Security & Privacy
- **Biometric Authentication** - Secure login with fingerprint/face recognition
- **End-to-End Encryption** - Your data is protected with industry-standard encryption
- **Row-Level Security** - Database-level access control with Supabase RLS
- **Session Management** - Automatic timeout and secure session handling

### 📱 Mobile-First Design
- **PWA Ready** - Install as a native app on any device
- **Offline Support** - Continue working without internet connection
- **Cross-Platform** - Optimized for iOS, Android, and desktop
- **Apple-Inspired UI** - Clean, minimalist design with smooth animations

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm
- Supabase account
- Modern web browser with PWA support

### Installation

1. **Clone the repository**
   ```bash
   git clone <YOUR_GIT_URL>
   cd vanta
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your Supabase credentials
   ```

4. **Set up Supabase database**
   ```bash
   # Run the migration files in your Supabase SQL editor:
   # 1. supabase/migrations/001_initial_schema.sql
   # 2. supabase/migrations/002_rls_policies.sql
   # 3. supabase/migrations/003_functions_triggers.sql
   ```

5. **Start development server**
   ```bash
   npm run dev
   ```

6. **Build for production**
   ```bash
   npm run build
   ```

### 🔧 Configuration

#### Environment Variables
Copy `.env.example` to `.env.local` and configure:

- `VITE_SUPABASE_URL` - Your Supabase project URL
- `VITE_SUPABASE_ANON_KEY` - Your Supabase anonymous key
- `VITE_ENABLE_BIOMETRIC_AUTH` - Enable biometric authentication
- `VITE_ENABLE_PWA` - Enable Progressive Web App features

#### Supabase Setup
1. Create a new Supabase project
2. Run the provided migration files in order
3. Enable Row Level Security (RLS) on all tables
4. Configure authentication providers as needed

## 🛠️ Tech Stack

### Frontend
- **React 18** - Modern React with hooks and concurrent features
- **TypeScript** - Type-safe development
- **Vite** - Lightning-fast build tool and dev server
- **Tailwind CSS** - Utility-first CSS framework
- **shadcn/ui** - Beautiful, accessible component library
- **Lucide React** - Consistent icon system

### Backend & Database
- **Supabase** - PostgreSQL database with real-time subscriptions
- **Row Level Security** - Database-level access control
- **Supabase Auth** - Authentication with multiple providers
- **Supabase Storage** - File storage and management

### PWA & Mobile
- **Service Worker** - Offline functionality and caching
- **Web App Manifest** - Native app-like experience
- **Responsive Design** - Mobile-first approach
- **Touch Gestures** - Swipe and tap interactions

### Security
- **Web Crypto API** - Client-side encryption
- **WebAuthn** - Biometric authentication
- **Content Security Policy** - XSS protection
- **Rate Limiting** - API abuse prevention

## 📱 Mobile Deployment

Vanta is designed for cross-platform mobile deployment:

### PWA Installation
- Automatic install prompts on supported browsers
- Offline functionality with service worker
- Native app-like experience
- Push notifications support

### Mobile Wrapper Options
- **Capacitor** - Recommended for iOS/Android deployment
- **Cordova** - Alternative mobile wrapper
- **Electron** - Desktop application wrapper

### Deployment Platforms
- **Vercel** - Recommended for web deployment
- **Netlify** - Alternative web hosting
- **App Store** - iOS deployment via Capacitor
- **Google Play** - Android deployment via Capacitor

## 🔒 Security Features

### Authentication
- Email/password authentication
- OAuth providers (Google, GitHub, etc.)
- Biometric authentication (WebAuthn)
- Session management with automatic timeout

### Data Protection
- End-to-end encryption for sensitive data
- Row-level security in database
- Secure session storage
- Input sanitization and validation

### Privacy
- No tracking or analytics by default
- Local data storage when possible
- Transparent data handling
- GDPR compliance ready

## 🤝 Contributing

We welcome contributions! Please see our contributing guidelines:

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

### Development Guidelines
- Follow TypeScript best practices
- Use conventional commit messages
- Maintain test coverage
- Update documentation

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **XSHLabs** - Development team
- **Supabase** - Backend infrastructure
- **shadcn** - UI component library
- **Lucide** - Icon system
- **Tailwind CSS** - Styling framework

## 📞 Support

For support and questions:
- Create an issue on GitHub
- Contact XSHLabs development team
- Check the documentation

---

**Built with ❤️ by XSHLabs**
