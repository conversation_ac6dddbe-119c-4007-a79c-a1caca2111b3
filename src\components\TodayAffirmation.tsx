import { <PERSON>, <PERSON>rkles } from "lucide-react";
import { useState, useEffect } from "react";

const affirmations = [
  "You are capable of amazing things today.",
  "Every challenge is an opportunity to grow stronger.",
  "Your potential is limitless and your spirit unbreakable.",
  "Today is full of possibilities waiting for you to discover.",
  "You have the power to create positive change in your life.",
  "Trust yourself - you know more than you think you do.",
  "Your presence makes a difference in this world.",
  "Be proud of how far you've come and excited for where you're going.",
];

export default function TodayAffirmation() {
  const [currentAffirmation, setCurrentAffirmation] = useState("");

  useEffect(() => {
    // Get a consistent affirmation for today based on the date
    const today = new Date().toDateString();
    const savedAffirmation = localStorage.getItem(`affirmation_${today}`);
    
    if (savedAffirmation) {
      setCurrentAffirmation(savedAffirmation);
    } else {
      const randomIndex = Math.floor(Math.random() * affirmations.length);
      const todayAffirmation = affirmations[randomIndex];
      setCurrentAffirmation(todayAffirmation);
      localStorage.setItem(`affirmation_${today}`, todayAffirmation);
    }
  }, []);

  return (
    <div className="relative overflow-hidden rounded-2xl p-6 bg-gradient-to-br from-primary/20 to-purple-500/10 border border-primary/20">
      <div className="flex items-start gap-4">
        <div className="p-3 rounded-xl bg-primary/20">
          <Heart className="h-6 w-6 text-primary" />
        </div>
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-3">
            <h3 className="font-semibold text-foreground">Today's Affirmation</h3>
            <Sparkles className="h-4 w-4 text-primary animate-pulse" />
          </div>
          <p className="text-foreground/90 italic leading-relaxed">
            "{currentAffirmation}"
          </p>
        </div>
      </div>

      {/* Animated background effect */}
      <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-primary/10 to-transparent rounded-full blur-2xl -translate-y-8 translate-x-8" />
    </div>
  );
}