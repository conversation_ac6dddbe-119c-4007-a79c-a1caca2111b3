// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://qtlzonnhzyvoumbyyfci.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF0bHpvbm5oenl2b3VtYnl5ZmNpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQ0ODczNjgsImV4cCI6MjA3MDA2MzM2OH0.NAQkB0E7It0WlrSy6o0ONadjbHj5mXGA_19hHFCcNHc";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});