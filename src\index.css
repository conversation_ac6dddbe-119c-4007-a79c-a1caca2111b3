@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* Vanta Dark Theme - Apple Inspired */
    --background: 0 0% 7%;
    --foreground: 0 0% 88%;

    --card: 0 0% 9%;
    --card-foreground: 0 0% 88%;

    --popover: 0 0% 9%;
    --popover-foreground: 0 0% 88%;

    --primary: 217 91% 60%;
    --primary-foreground: 0 0% 98%;

    --secondary: 0 0% 12%;
    --secondary-foreground: 0 0% 88%;

    --muted: 0 0% 12%;
    --muted-foreground: 0 0% 55%;

    --accent: 0 0% 14%;
    --accent-foreground: 0 0% 88%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 15%;
    --input: 0 0% 12%;
    --ring: 217 91% 60%;

    --radius: 1rem;

    /* Custom Vanta Colors - Enhanced Apple-inspired Dark Theme */
    --vanta-bg: 0 0% 7%;
    --vanta-surface: 0 0% 9%;
    --vanta-surface-hover: 0 0% 12%;
    --vanta-text-primary: 0 0% 88%;
    --vanta-text-secondary: 0 0% 65%;
    --vanta-text-muted: 0 0% 45%;
    --vanta-accent: 217 91% 60%;
    --vanta-accent-soft: 217 50% 20%;
    --vanta-success: 142 71% 45%;
    --vanta-warning: 38 92% 50%;
    --vanta-purple: 270 91% 65%;
    --vanta-purple-soft: 270 50% 25%;

    /* Enhanced Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(var(--vanta-accent)), hsl(217 70% 50%));
    --gradient-surface: linear-gradient(135deg, hsl(var(--vanta-surface)), hsl(0 0% 11%));
    --gradient-purple: linear-gradient(135deg, hsl(var(--vanta-purple)), hsl(270 70% 55%));
    --gradient-success: linear-gradient(135deg, hsl(var(--vanta-success)), hsl(142 60% 40%));

    /* Enhanced Shadows */
    --shadow-elegant: 0 10px 30px -10px hsl(0 0% 0% / 0.5);
    --shadow-glow: 0 0 40px hsl(var(--vanta-accent) / 0.15);
    --shadow-card: 0 4px 20px -4px hsl(0 0% 0% / 0.25);
    --shadow-floating: 0 20px 40px -12px hsl(0 0% 0% / 0.4);

    /* Enhanced Animations */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-spring: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    --transition-bounce: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

@layer components {
  /* Premium Apple-inspired card styles */
  .vanta-card {
    @apply bg-card/50 border border-border/30 rounded-2xl backdrop-blur-xl;
    background: rgba(255, 255, 255, 0.02);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .vanta-card:hover {
    background: rgba(255, 255, 255, 0.04);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.16);
    transform: translateY(-1px);
  }

  /* Clean button styles */
  .vanta-button {
    @apply rounded-xl font-medium transition-all duration-200;
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .vanta-button:hover {
    background: rgba(255, 255, 255, 0.12);
    transform: translateY(-0.5px);
  }

  /* Premium glassmorphism effect */
  .vanta-glass {
    background: rgba(255, 255, 255, 0.03);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.08);
  }

  /* Premium text styles */
  .vanta-title {
    @apply text-foreground font-semibold tracking-tight;
    font-weight: 600;
  }

  .vanta-subtitle {
    @apply text-muted-foreground text-sm;
    font-weight: 400;
  }

  /* Clean stat cards */
  .vanta-stat-card {
    @apply vanta-card p-6 text-center;
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.06);
  }

  /* Minimal icons */
  .vanta-icon {
    @apply w-5 h-5 text-muted-foreground;
    stroke-width: 1.5;
  }

  /* Clean input styles */
  .vanta-input {
    @apply bg-transparent border border-border/30 rounded-xl px-4 py-3;
    background: rgba(255, 255, 255, 0.02);
    transition: all 0.2s ease;
  }

  .vanta-input:focus {
    border-color: rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.04);
    outline: none;
  }

  /* Smooth animations */
  .vanta-fade-in {
    animation: fade-in 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .vanta-slide-up {
    animation: slide-up 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .vanta-bounce {
    animation: bounce 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  }

  /* Custom scrollbar */
  .vanta-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .vanta-scrollbar::-webkit-scrollbar-track {
    background: hsl(var(--muted));
    border-radius: 3px;
  }

  .vanta-scrollbar::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground));
    border-radius: 3px;
  }

  .vanta-scrollbar::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--primary));
  }

  /* Mobile-first responsive design */
  .vanta-mobile-padding {
    @apply px-4 sm:px-6 lg:px-8;
  }

  .vanta-mobile-text {
    @apply text-sm sm:text-base;
  }

  .vanta-mobile-grid {
    @apply grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4;
  }

  /* Touch-friendly interactions */
  .vanta-touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Safe area support for mobile devices */
  .vanta-safe-area {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }

  /* Enhanced mobile navigation */
  .vanta-mobile-nav {
    @apply fixed bottom-0 left-0 right-0 z-50 md:hidden;
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* Improved focus states for accessibility */
  .vanta-focus {
    @apply focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-background;
  }

  /* Cross-platform font optimization */
  .vanta-text-optimized {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }

  /* PWA-ready styles */
  .vanta-pwa-ready {
    -webkit-user-select: none;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
  }

  /* Gesture support */
  .vanta-swipeable {
    touch-action: pan-x pan-y;
  }

  .vanta-no-scroll {
    touch-action: none;
    overscroll-behavior: none;
  }
}

/* Mobile-specific media queries */
@media (max-width: 768px) {
  .vanta-card {
    @apply rounded-xl;
    margin-bottom: 1rem;
  }

  .vanta-mobile-stack {
    @apply flex-col space-y-4;
  }

  .vanta-mobile-full {
    @apply w-full;
  }
}

/* Tablet-specific optimizations */
@media (min-width: 768px) and (max-width: 1024px) {
  .vanta-tablet-grid {
    @apply grid-cols-2 lg:grid-cols-3;
  }
}

/* High DPI display support */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .vanta-high-dpi {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Dark mode optimizations */
@media (prefers-color-scheme: dark) {
  .vanta-auto-dark {
    color-scheme: dark;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .vanta-fade-in,
  .vanta-slide-up,
  .vanta-bounce {
    animation: none;
  }

  .vanta-card:hover {
    transform: none;
  }

  .vanta-button:hover {
    transform: none;
  }
}

/* Print styles */
@media print {
  .vanta-no-print {
    display: none !important;
  }

  .vanta-card {
    box-shadow: none;
    border: 1px solid #ccc;
  }
}